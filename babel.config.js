module.exports = function(api) {
  const isTest = api.env('test');
  api.cache(true);

  return {
    presets: isTest
      ? [
          ['@babel/preset-env', { targets: { node: 'current' } }],
          ['@babel/preset-react', { runtime: 'automatic' }],
        ]
      : [
          ['babel-preset-expo', { jsxImportSource: 'nativewind' }]
        ],
    plugins: isTest ? [] : ['nativewind/babel'],
  };
};
