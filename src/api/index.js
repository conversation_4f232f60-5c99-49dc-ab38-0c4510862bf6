/**
 * OnRoad API Service
 * Based on the OpenAPI 3.0 specification for OnRoad Real Estate API
 * Integrated with Supabase authentication
 */

import { API_CONFIG, API_ENDPOINTS, HTTP_STATUS, API_ERRORS } from '../config/api';
import authService from '../services/auth';

class ApiService {
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
    this.retryConfig = API_CONFIG.RETRY_CONFIG;
  }

  /**
   * Get authentication headers
   * @returns {Object} Headers object with authorization
   */
  getAuthHeaders() {
    const headers = {
      ...API_CONFIG.DEFAULT_HEADERS,
    };

    // Get token from auth service
    const token = authService.getAccessToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Check if user is authenticated
   * @throws {Error} If user is not authenticated
   */
  requireAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error(API_ERRORS.UNAUTHORIZED);
    }
  }

  /**
   * Make authenticated API request with retry logic
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Fetch options
   * @param {number} retryCount - Current retry count
   * @returns {Promise} API response
   */
  async makeRequest(endpoint, options = {}, retryCount = 0) {
    // Check authentication for protected endpoints
    if (!endpoint.includes('/auth/') && !endpoint.includes('/public/')) {
      this.requireAuth();
    }

    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getAuthHeaders(),
      timeout: this.timeout,
      ...options,
    };

    try {
      if (API_CONFIG.DEBUG) {
        console.log(`API Request: ${config.method || 'GET'} ${url}`);
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Handle different response status codes
      if (response.status === HTTP_STATUS.UNAUTHORIZED) {
        // Token might be expired, try to refresh
        const refreshResult = await authService.refreshSession();
        if (refreshResult.success && retryCount < 1) {
          return this.makeRequest(endpoint, options, retryCount + 1);
        }
        throw new Error(API_ERRORS.UNAUTHORIZED);
      }

      if (response.status === HTTP_STATUS.FORBIDDEN) {
        throw new Error(API_ERRORS.FORBIDDEN);
      }

      if (response.status === HTTP_STATUS.NOT_FOUND) {
        throw new Error(API_ERRORS.NOT_FOUND);
      }

      if (!response.ok) {
        // Check if we should retry
        if (this.retryConfig.retryOn.includes(response.status) &&
            retryCount < this.retryConfig.maxRetries) {
          await this.delay(this.retryConfig.retryDelay * (retryCount + 1));
          return this.makeRequest(endpoint, options, retryCount + 1);
        }

        const errorText = await response.text();
        throw new Error(`API Error: ${response.status} ${errorText || response.statusText}`);
      }

      // Handle no content responses
      if (response.status === HTTP_STATUS.NO_CONTENT) {
        return null;
      }

      const data = await response.json();

      if (API_CONFIG.DEBUG) {
        console.log(`API Response: ${response.status}`, data);
      }

      return data;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error(API_ERRORS.TIMEOUT_ERROR);
      }

      if (error.message.includes('Network request failed')) {
        throw new Error(API_ERRORS.NETWORK_ERROR);
      }

      console.error('API Request failed:', error);
      throw error;
    }
  }

  /**
   * Delay utility for retry logic
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Delay promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Authentication Methods

  /**
   * Execute first sign-in logic for a new user
   * @returns {Promise} First sign-in response
   */
  async firstSignIn() {
    return this.makeRequest(API_ENDPOINTS.FIRST_SIGNIN, {
      method: 'POST',
    });
  }

  // Profile Methods

  /**
   * Get current user profile
   * @returns {Promise} User profile data
   */
  async getProfile() {
    return this.makeRequest(API_ENDPOINTS.PROFILES);
  }

  /**
   * Create a new user profile
   * @param {Object} profileData - Profile information
   * @returns {Promise} Created profile
   */
  async createProfile(profileData) {
    return this.makeRequest(API_ENDPOINTS.PROFILES, {
      method: 'POST',
      body: JSON.stringify(profileData),
    });
  }

  // Deal Methods

  /**
   * Get all deals for the authenticated user
   * @returns {Promise} Array of deals
   */
  async getDeals() {
    return this.makeRequest(API_ENDPOINTS.DEALS);
  }

  /**
   * Create a new deal
   * @param {Object} dealData - Deal information
   * @returns {Promise} Created deal
   */
  async createDeal(dealData) {
    return this.makeRequest(API_ENDPOINTS.DEALS, {
      method: 'POST',
      body: JSON.stringify(dealData),
    });
  }

  /**
   * Get a specific deal by ID
   * @param {string} dealId - Deal ID
   * @returns {Promise} Deal details
   */
  async getDeal(dealId) {
    return this.makeRequest(API_ENDPOINTS.DEAL_BY_ID(dealId));
  }

  // Property Methods

  /**
   * Get properties for a deal
   * @param {string} dealId - Deal ID
   * @returns {Promise} Array of properties
   */
  async getProperties(dealId) {
    return this.makeRequest(API_ENDPOINTS.PROPERTIES(dealId));
  }

  /**
   * Add a new property to a deal
   * @param {string} dealId - Deal ID
   * @param {Object} propertyData - Property information
   * @returns {Promise} Created property
   */
  async createProperty(dealId, propertyData) {
    return this.makeRequest(API_ENDPOINTS.PROPERTIES(dealId), {
      method: 'POST',
      body: JSON.stringify(propertyData),
    });
  }

  /**
   * Update a property
   * @param {string} propertyId - Property ID
   * @param {Object} propertyData - Updated property information
   * @returns {Promise} Updated property
   */
  async updateProperty(propertyId, propertyData) {
    return this.makeRequest(API_ENDPOINTS.PROPERTY_BY_ID(propertyId), {
      method: 'PUT',
      body: JSON.stringify(propertyData),
    });
  }

  /**
   * Delete a property
   * @param {string} propertyId - Property ID
   * @returns {Promise} Deletion confirmation
   */
  async deleteProperty(propertyId) {
    return this.makeRequest(API_ENDPOINTS.PROPERTY_BY_ID(propertyId), {
      method: 'DELETE',
    });
  }

  // Form Methods

  /**
   * Get forms for a property
   * @param {string} propertyId - Property ID
   * @returns {Promise} Array of forms
   */
  async getForms(propertyId) {
    return this.makeRequest(API_ENDPOINTS.FORMS_BY_PROPERTY(propertyId));
  }

  /**
   * Add a new form to a property
   * @param {string} propertyId - Property ID
   * @param {Object} formData - Form information
   * @returns {Promise} Created form
   */
  async createForm(propertyId, formData) {
    return this.makeRequest(API_ENDPOINTS.FORMS_BY_PROPERTY(propertyId), {
      method: 'POST',
      body: JSON.stringify(formData),
    });
  }

  /**
   * Update a form
   * @param {string} formId - Form ID
   * @param {Object} formData - Updated form information
   * @returns {Promise} Updated form
   */
  async updateForm(formId, formData) {
    return this.makeRequest(API_ENDPOINTS.FORM_BY_ID(formId), {
      method: 'PUT',
      body: JSON.stringify(formData),
    });
  }

  /**
   * Delete a form
   * @param {string} formId - Form ID
   * @returns {Promise} Deletion confirmation
   */
  async deleteForm(formId) {
    return this.makeRequest(API_ENDPOINTS.FORM_BY_ID(formId), {
      method: 'DELETE',
    });
  }

  // Client Intake Methods

  /**
   * Get realtor intake schema for a client
   * @param {string} searchId - Search ID
   * @returns {Promise} Intake schema
   */
  async getClientIntakeSchema(searchId) {
    return this.makeRequest(API_ENDPOINTS.CLIENT_INTAKE_SCHEMA(searchId));
  }

  /**
   * Get client intake form responses
   * @param {string} searchId - Search ID
   * @returns {Promise} Intake form responses
   */
  async getClientIntakeAnswers(searchId) {
    return this.makeRequest(API_ENDPOINTS.CLIENT_INTAKE_ANSWERS(searchId));
  }

  /**
   * Get client intake schema by deal ID
   * @param {string} dealId - Deal ID
   * @returns {Promise} Intake schema
   */
  async getRealtorClientIntake(dealId) {
    return this.makeRequest(API_ENDPOINTS.REALTOR_CLIENT_INTAKE(dealId));
  }

  /**
   * Publish client intake template to tether
   * @param {string} dealId - Deal ID
   * @returns {Promise} Publish confirmation
   */
  async publishIntakeTemplate(dealId) {
    return this.makeRequest(API_ENDPOINTS.PUBLISH_INTAKE_TEMPLATE(dealId), {
      method: 'PATCH',
    });
  }

  // Dashboard Methods

  /**
   * Get dashboard statistics
   * @returns {Promise} Dashboard statistics including deals and properties count
   */
  async getDashboardStats() {
    try {
      // Get deals data to calculate statistics
      const deals = await this.getDeals();

      // Calculate statistics from deals data
      const totalDeals = deals?.length || 0;
      const activeDeals = deals?.filter(deal =>
        deal.status === 'active' || deal.status === 'in_progress' || deal.status === 'pending'
      ).length || 0;
      const completedDeals = deals?.filter(deal =>
        deal.status === 'completed' || deal.status === 'closed'
      ).length || 0;

      // Get total properties count across all deals
      let totalProperties = 0;
      if (deals && deals.length > 0) {
        for (const deal of deals) {
          try {
            const properties = await this.getProperties(deal.id);
            totalProperties += properties?.length || 0;
          } catch (error) {
            // Continue if properties can't be fetched for a deal
            console.warn(`Could not fetch properties for deal ${deal.id}:`, error.message);
          }
        }
      }

      return {
        totalDeals,
        activeDeals,
        completedDeals,
        totalProperties,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Return default stats if API fails
      return {
        totalDeals: 0,
        activeDeals: 0,
        completedDeals: 0,
        totalProperties: 0,
        lastUpdated: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Get recent activity data
   * @param {number} limit - Number of recent activities to fetch
   * @returns {Promise} Array of recent activities
   */
  async getRecentActivity(limit = 10) {
    try {
      const deals = await this.getDeals();
      const activities = [];

      // Generate activity items from deals data
      if (deals && deals.length > 0) {
        // Sort deals by updated date (most recent first)
        const sortedDeals = deals
          .filter(deal => deal.updatedAt || deal.createdAt)
          .sort((a, b) => {
            const dateA = new Date(a.updatedAt || a.createdAt);
            const dateB = new Date(b.updatedAt || b.createdAt);
            return dateB - dateA;
          })
          .slice(0, limit);

        for (const deal of sortedDeals) {
          // Add deal update activity
          activities.push({
            id: `deal-${deal.id}`,
            type: 'deal_update',
            title: `Deal #${deal.id} updated`,
            description: deal.title || deal.name || 'Deal information updated',
            timestamp: deal.updatedAt || deal.createdAt,
            color: 'green'
          });

          // Try to get properties for additional activities
          try {
            const properties = await this.getProperties(deal.id);
            if (properties && properties.length > 0) {
              const recentProperty = properties
                .filter(prop => prop.createdAt)
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];

              if (recentProperty) {
                activities.push({
                  id: `property-${recentProperty.id}`,
                  type: 'property_added',
                  title: 'New property added',
                  description: recentProperty.address || recentProperty.title || 'Property added to deal',
                  timestamp: recentProperty.createdAt,
                  color: 'blue'
                });
              }
            }
          } catch (error) {
            // Continue if properties can't be fetched
            console.warn(`Could not fetch properties for deal ${deal.id}:`, error.message);
          }
        }
      }

      // Sort all activities by timestamp and limit
      return activities
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);

    } catch (error) {
      console.error('Error fetching recent activity:', error);
      // Return default activities if API fails
      return [
        {
          id: 'default-1',
          type: 'system',
          title: 'Welcome to OnRoad',
          description: 'Start by creating your first deal',
          timestamp: new Date().toISOString(),
          color: 'blue'
        }
      ];
    }
  }

  /**
   * Get comprehensive dashboard data
   * @returns {Promise} Complete dashboard data including stats, activities, and profile
   */
  async getDashboardData() {
    try {
      const [stats, activities, profile] = await Promise.allSettled([
        this.getDashboardStats(),
        this.getRecentActivity(),
        this.getProfile().catch(() => null) // Profile might not exist yet
      ]);

      return {
        stats: stats.status === 'fulfilled' ? stats.value : {
          totalDeals: 0,
          activeDeals: 0,
          completedDeals: 0,
          totalProperties: 0,
          error: stats.reason?.message
        },
        activities: activities.status === 'fulfilled' ? activities.value : [],
        profile: profile.status === 'fulfilled' ? profile.value : null,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const apiService = new ApiService();

export default apiService;
