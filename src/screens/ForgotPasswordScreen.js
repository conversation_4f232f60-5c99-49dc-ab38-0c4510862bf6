import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import authService from '../services/auth';

const ForgotPasswordSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
});

const ForgotPasswordScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const handleResetPassword = async (values) => {
    setIsLoading(true);
    
    try {
      console.log('Password reset attempt:', values.email);
      
      // Attempt to reset password with Supabase
      const result = await authService.resetPassword(values.email);
      
      if (result.success) {
        setEmailSent(true);
        Alert.alert(
          'Success', 
          result.message || 'Password reset email sent! Please check your inbox.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate back to login screen
                navigation.navigate('Login');
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Password reset failed. Please try again.');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView 
          className="flex-1" 
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="flex-1 px-6 pt-15 pb-10">
            {/* App Logo/Title */}
            <View className="items-center mb-15">
              <Text className="text-4xl font-bold text-gray-800 mb-2">OnRoad</Text>
              <Text className="text-base text-gray-500 text-center">Real Estate Transactions Made Simple</Text>
            </View>

            {/* Reset Password Form */}
            <View className="justify-center">
              <Text className="text-3xl font-bold text-gray-800 mb-2 text-center">Reset Password</Text>
              <Text className="text-base text-gray-500 text-center mb-10">
                Enter your email address and we'll send you a link to reset your password.
              </Text>

              {!emailSent ? (
                <Formik
                  initialValues={{ email: '' }}
                  validationSchema={ForgotPasswordSchema}
                  onSubmit={handleResetPassword}
                >
                  {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                    <>
                      <View className="mb-5">
                        <Text className="text-base font-semibold text-gray-800 mb-2">Email</Text>
                        <TextInput
                          className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                            errors.email && touched.email ? 'border-red-500' : 'border-gray-200'
                          }`}
                          placeholder="Enter your email"
                          autoCapitalize="none"
                          keyboardType="email-address"
                          autoComplete="email"
                          onChangeText={handleChange('email')}
                          onBlur={handleBlur('email')}
                          value={values.email}
                          editable={!isLoading}
                        />
                        {errors.email && touched.email && (
                          <Text className="text-red-500 text-sm mt-1 ml-1">{errors.email}</Text>
                        )}
                      </View>

                      <TouchableOpacity
                        className={`rounded-xl p-4 items-center mt-5 ${
                          isLoading ? 'bg-gray-400' : 'bg-black shadow-lg'
                        }`}
                        onPress={handleSubmit}
                        disabled={isLoading}
                      >
                        <Text className="text-white text-lg font-semibold">
                          {isLoading ? 'Sending...' : 'Send Reset Link'}
                        </Text>
                      </TouchableOpacity>
                    </>
                  )}
                </Formik>
              ) : (
                <View className="items-center">
                  <View className="bg-green-100 border border-green-400 rounded-xl p-4 mb-5">
                    <Text className="text-green-700 text-center">
                      Password reset email sent! Please check your inbox and follow the instructions to reset your password.
                    </Text>
                  </View>
                  
                  <TouchableOpacity
                    className="rounded-xl p-4 items-center bg-black shadow-lg"
                    onPress={() => navigation.navigate('Login')}
                  >
                    <Text className="text-white text-lg font-semibold">
                      Back to Login
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>

            {/* Footer Links */}
            <View className="items-center pt-10">
              <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                <Text className="text-blue-500 text-base font-medium mb-5">Back to Login</Text>
              </TouchableOpacity>

              <View className="flex-row items-center">
                <Text className="text-base text-gray-500">Don't have an account? </Text>
                <TouchableOpacity onPress={() => navigation.navigate('SignUp')}>
                  <Text className="text-base text-blue-500 font-semibold">Sign Up</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ForgotPasswordScreen;
