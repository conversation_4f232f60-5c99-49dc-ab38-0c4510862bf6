import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl
} from 'react-native';
import authService from '../services/auth';
import apiService from '../api';

const DashboardScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalDeals: 0,
    activeDeals: 0,
    completedDeals: 0,
    totalProperties: 0
  });

  useEffect(() => {
    loadUserData();
    loadDashboardData();
  }, []);

  const loadUserData = () => {
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  };

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      // In a real app, you would load actual dashboard data
      // For now, we'll simulate some data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        totalDeals: 12,
        activeDeals: 8,
        completedDeals: 4,
        totalProperties: 25
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await authService.signOut();
              if (result.success) {
                // Navigate back to login screen
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Login' }],
                });
              } else {
                Alert.alert('Error', 'Failed to logout. Please try again.');
              }
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'An unexpected error occurred during logout.');
            }
          },
        },
      ]
    );
  };

  const StatCard = ({ title, value, subtitle, color = 'bg-white' }) => (
    <View className={`${color} rounded-xl p-4 shadow-sm border border-gray-100 flex-1 mx-1`}>
      <Text className="text-2xl font-bold text-gray-800">{value}</Text>
      <Text className="text-base font-semibold text-gray-700 mt-1">{title}</Text>
      {subtitle && (
        <Text className="text-sm text-gray-500 mt-1">{subtitle}</Text>
      )}
    </View>
  );

  const QuickActionButton = ({ title, subtitle, onPress, color = 'bg-blue-500' }) => (
    <TouchableOpacity
      className={`${color} rounded-xl p-4 mb-3 shadow-sm`}
      onPress={onPress}
    >
      <Text className="text-white text-lg font-semibold">{title}</Text>
      {subtitle && (
        <Text className="text-blue-100 text-sm mt-1">{subtitle}</Text>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View className="bg-white px-6 py-4 border-b border-gray-100">
          <View className="flex-row justify-between items-center">
            <View>
              <Text className="text-2xl font-bold text-gray-800">OnRoad</Text>
              <Text className="text-sm text-gray-500">Real Estate Dashboard</Text>
            </View>
            <TouchableOpacity
              className="bg-red-500 px-4 py-2 rounded-lg"
              onPress={handleLogout}
            >
              <Text className="text-white font-semibold">Logout</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View className="px-6 py-6">
          {/* Welcome Section */}
          <View className="mb-6">
            <Text className="text-xl font-bold text-gray-800 mb-2">
              Welcome back{user?.email ? `, ${user.email.split('@')[0]}` : ''}!
            </Text>
            <Text className="text-gray-600">
              Here's what's happening with your real estate transactions today.
            </Text>
            {user?.email && (
              <Text className="text-sm text-gray-500 mt-2">
                Logged in as: {user.email}
              </Text>
            )}
          </View>

          {/* Stats Overview */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-gray-800 mb-4">Overview</Text>
            <View className="flex-row mb-3">
              <StatCard
                title="Total Deals"
                value={stats.totalDeals}
                subtitle="All time"
                color="bg-blue-50"
              />
              <StatCard
                title="Active Deals"
                value={stats.activeDeals}
                subtitle="In progress"
                color="bg-green-50"
              />
            </View>
            <View className="flex-row">
              <StatCard
                title="Completed"
                value={stats.completedDeals}
                subtitle="This month"
                color="bg-purple-50"
              />
              <StatCard
                title="Properties"
                value={stats.totalProperties}
                subtitle="Total listings"
                color="bg-orange-50"
              />
            </View>
          </View>

          {/* Quick Actions */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-gray-800 mb-4">Quick Actions</Text>
            <QuickActionButton
              title="Create New Deal"
              subtitle="Start a new real estate transaction"
              onPress={() => Alert.alert('Feature Coming Soon', 'Deal creation will be available soon!')}
              color="bg-blue-500"
            />
            <QuickActionButton
              title="View All Deals"
              subtitle="Manage your existing deals"
              onPress={() => Alert.alert('Feature Coming Soon', 'Deal management will be available soon!')}
              color="bg-green-500"
            />
            <QuickActionButton
              title="Add Property"
              subtitle="List a new property"
              onPress={() => Alert.alert('Feature Coming Soon', 'Property management will be available soon!')}
              color="bg-purple-500"
            />
          </View>

          {/* Recent Activity */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-gray-800 mb-4">Recent Activity</Text>
            <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <View className="flex-row items-center mb-3">
                <View className="w-3 h-3 bg-green-500 rounded-full mr-3"></View>
                <View className="flex-1">
                  <Text className="text-gray-800 font-medium">Deal #1234 updated</Text>
                  <Text className="text-gray-500 text-sm">2 hours ago</Text>
                </View>
              </View>
              <View className="flex-row items-center mb-3">
                <View className="w-3 h-3 bg-blue-500 rounded-full mr-3"></View>
                <View className="flex-1">
                  <Text className="text-gray-800 font-medium">New property added</Text>
                  <Text className="text-gray-500 text-sm">5 hours ago</Text>
                </View>
              </View>
              <View className="flex-row items-center">
                <View className="w-3 h-3 bg-orange-500 rounded-full mr-3"></View>
                <View className="flex-1">
                  <Text className="text-gray-800 font-medium">Client intake completed</Text>
                  <Text className="text-gray-500 text-sm">1 day ago</Text>
                </View>
              </View>
            </View>
          </View>

          {/* App Info */}
          <View className="bg-gray-100 rounded-xl p-4 mt-4">
            <Text className="text-center text-gray-600 text-sm">
              OnRoad Real Estate Platform
            </Text>
            <Text className="text-center text-gray-500 text-xs mt-1">
              Version 1.0.0 • {user ? 'Authenticated' : 'Not authenticated'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DashboardScreen;
