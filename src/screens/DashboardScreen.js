import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl
} from 'react-native';
import authService from '../services/auth';
import apiService from '../api';
import { testFallbackData } from '../utils/testFallback';

const DashboardScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalDeals: 0,
    activeDeals: 0,
    completedDeals: 0,
    totalProperties: 0
  });
  const [activities, setActivities] = useState([]);
  const [error, setError] = useState(null);
  const [isSampleData, setIsSampleData] = useState(false);

  useEffect(() => {
    // Test fallback data
    testFallbackData();

    loadUserData();
    loadDashboardData();
  }, []);

  const loadUserData = () => {
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  };

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('[DASHBOARD] Loading dashboard data from API...');

      // Fetch comprehensive dashboard data from API
      const dashboardData = await apiService.getDashboardData();

      console.log('[DASHBOARD] Dashboard data loaded:', {
        hasStats: !!dashboardData.stats,
        hasActivities: !!dashboardData.activities,
        hasProfile: !!dashboardData.profile,
        statsError: dashboardData.stats?.error
      });

      // Update stats
      if (dashboardData.stats) {
        setStats({
          totalDeals: dashboardData.stats.totalDeals || 0,
          activeDeals: dashboardData.stats.activeDeals || 0,
          completedDeals: dashboardData.stats.completedDeals || 0,
          totalProperties: dashboardData.stats.totalProperties || 0
        });

        // Check if we're using sample data
        setIsSampleData(dashboardData.stats.isSampleData || false);

        // Log any API errors but don't fail the whole dashboard
        if (dashboardData.stats.error) {
          console.warn('[DASHBOARD] Stats API error:', dashboardData.stats.error);
        }
      }

      // Update activities
      if (dashboardData.activities) {
        setActivities(dashboardData.activities);
      }

      // Update profile if available
      if (dashboardData.profile) {
        setProfile(dashboardData.profile);
      }

      console.log('[DASHBOARD] Dashboard data loaded successfully');

    } catch (error) {
      console.error('[DASHBOARD] Error loading dashboard data:', error);
      setError(error.message);

      // Set default values on error
      setStats({
        totalDeals: 0,
        activeDeals: 0,
        completedDeals: 0,
        totalProperties: 0
      });
      setActivities([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await authService.signOut();
              if (result.success) {
                // Navigate back to login screen
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Login' }],
                });
              } else {
                Alert.alert('Error', 'Failed to logout. Please try again.');
              }
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'An unexpected error occurred during logout.');
            }
          },
        },
      ]
    );
  };

  const formatActivityTime = (timestamp) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'Just now';
      if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
      if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
      if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;

      return date.toLocaleDateString();
    } catch (error) {
      return 'Recently';
    }
  };

  const StatCard = ({ title, value, subtitle, color = 'bg-white' }) => (
    <View className={`${color} rounded-xl p-4 shadow-sm border border-gray-100 flex-1 mx-1`}>
      <Text className="text-2xl font-bold text-gray-800">{value}</Text>
      <Text className="text-base font-semibold text-gray-700 mt-1">{title}</Text>
      {subtitle && (
        <Text className="text-sm text-gray-500 mt-1">{subtitle}</Text>
      )}
    </View>
  );

  const QuickActionButton = ({ title, subtitle, onPress, color = 'bg-blue-500' }) => (
    <TouchableOpacity
      className={`${color} rounded-xl p-4 mb-3 shadow-sm`}
      onPress={onPress}
    >
      <Text className="text-white text-lg font-semibold">{title}</Text>
      {subtitle && (
        <Text className="text-blue-100 text-sm mt-1">{subtitle}</Text>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View className="bg-white px-6 py-4 border-b border-gray-100">
          <View className="flex-row justify-between items-center">
            <View>
              <Text className="text-2xl font-bold text-gray-800">OnRoad</Text>
              <Text className="text-sm text-gray-500">Real Estate Dashboard</Text>
            </View>
            <TouchableOpacity
              className="bg-red-500 px-4 py-2 rounded-lg"
              onPress={handleLogout}
            >
              <Text className="text-white font-semibold">Logout</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View className="px-6 py-6">
          {/* Welcome Section */}
          <View className="mb-6">
            <Text className="text-xl font-bold text-gray-800 mb-2">
              Welcome back{profile?.firstName ? `, ${profile.firstName}` : user?.email ? `, ${user.email.split('@')[0]}` : ''}!
            </Text>
            <Text className="text-gray-600">
              Here's what's happening with your real estate transactions today.
            </Text>
            {profile && (
              <Text className="text-sm text-gray-500 mt-2">
                {profile.role && `Role: ${profile.role.charAt(0).toUpperCase() + profile.role.slice(1)} • `}
                {profile.email || user?.email}
              </Text>
            )}
            {!profile && user?.email && (
              <Text className="text-sm text-gray-500 mt-2">
                Logged in as: {user.email}
              </Text>
            )}
            {error && (
              <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3">
                <Text className="text-yellow-800 text-sm">
                  ⚠️ Some data may be limited due to API connectivity issues
                </Text>
              </View>
            )}
            {isSampleData && (
              <View className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
                <Text className="text-blue-800 text-sm">
                  📊 Showing sample data for demonstration. Create your first deal to see real data here.
                </Text>
              </View>
            )}
          </View>

          {/* Stats Overview */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-gray-800 mb-4">Overview</Text>
            {isLoading ? (
              <View className="flex-row mb-3">
                <View className="bg-gray-100 rounded-xl p-4 shadow-sm border border-gray-100 flex-1 mx-1">
                  <View className="bg-gray-200 h-8 w-12 rounded mb-2"></View>
                  <View className="bg-gray-200 h-4 w-20 rounded mb-1"></View>
                  <View className="bg-gray-200 h-3 w-16 rounded"></View>
                </View>
                <View className="bg-gray-100 rounded-xl p-4 shadow-sm border border-gray-100 flex-1 mx-1">
                  <View className="bg-gray-200 h-8 w-12 rounded mb-2"></View>
                  <View className="bg-gray-200 h-4 w-20 rounded mb-1"></View>
                  <View className="bg-gray-200 h-3 w-16 rounded"></View>
                </View>
              </View>
            ) : (
              <>
                <View className="flex-row mb-3">
                  <StatCard
                    title="Total Deals"
                    value={stats.totalDeals}
                    subtitle="All time"
                    color="bg-blue-50"
                  />
                  <StatCard
                    title="Active Deals"
                    value={stats.activeDeals}
                    subtitle="In progress"
                    color="bg-green-50"
                  />
                </View>
                <View className="flex-row">
                  <StatCard
                    title="Completed"
                    value={stats.completedDeals}
                    subtitle="This month"
                    color="bg-purple-50"
                  />
                  <StatCard
                    title="Properties"
                    value={stats.totalProperties}
                    subtitle="Total listings"
                    color="bg-orange-50"
                  />
                </View>
              </>
            )}
          </View>

          {/* Quick Actions */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-gray-800 mb-4">Quick Actions</Text>
            <QuickActionButton
              title="Create New Deal"
              subtitle="Start a new real estate transaction"
              onPress={() => Alert.alert('Feature Coming Soon', 'Deal creation will be available soon!')}
              color="bg-blue-500"
            />
            <QuickActionButton
              title="View All Deals"
              subtitle="Manage your existing deals"
              onPress={() => Alert.alert('Feature Coming Soon', 'Deal management will be available soon!')}
              color="bg-green-500"
            />
            <QuickActionButton
              title="Add Property"
              subtitle="List a new property"
              onPress={() => Alert.alert('Feature Coming Soon', 'Property management will be available soon!')}
              color="bg-purple-500"
            />
          </View>

          {/* Recent Activity */}
          <View className="mb-6">
            <Text className="text-lg font-bold text-gray-800 mb-4">Recent Activity</Text>
            <View className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              {isLoading ? (
                <>
                  <View className="flex-row items-center mb-3">
                    <View className="w-3 h-3 bg-gray-200 rounded-full mr-3"></View>
                    <View className="flex-1">
                      <View className="bg-gray-200 h-4 w-32 rounded mb-1"></View>
                      <View className="bg-gray-200 h-3 w-20 rounded"></View>
                    </View>
                  </View>
                  <View className="flex-row items-center mb-3">
                    <View className="w-3 h-3 bg-gray-200 rounded-full mr-3"></View>
                    <View className="flex-1">
                      <View className="bg-gray-200 h-4 w-28 rounded mb-1"></View>
                      <View className="bg-gray-200 h-3 w-16 rounded"></View>
                    </View>
                  </View>
                  <View className="flex-row items-center">
                    <View className="w-3 h-3 bg-gray-200 rounded-full mr-3"></View>
                    <View className="flex-1">
                      <View className="bg-gray-200 h-4 w-36 rounded mb-1"></View>
                      <View className="bg-gray-200 h-3 w-24 rounded"></View>
                    </View>
                  </View>
                </>
              ) : activities.length > 0 ? (
                activities.map((activity, index) => (
                  <View key={activity.id} className={`flex-row items-center ${index < activities.length - 1 ? 'mb-3' : ''}`}>
                    <View className={`w-3 h-3 bg-${activity.color}-500 rounded-full mr-3`}></View>
                    <View className="flex-1">
                      <Text className="text-gray-800 font-medium">{activity.title}</Text>
                      <Text className="text-gray-500 text-sm">{formatActivityTime(activity.timestamp)}</Text>
                      {activity.description && (
                        <Text className="text-gray-600 text-xs mt-1">{activity.description}</Text>
                      )}
                    </View>
                  </View>
                ))
              ) : (
                <View className="flex-row items-center">
                  <View className="w-3 h-3 bg-blue-500 rounded-full mr-3"></View>
                  <View className="flex-1">
                    <Text className="text-gray-800 font-medium">Welcome to OnRoad</Text>
                    <Text className="text-gray-500 text-sm">Start by creating your first deal</Text>
                  </View>
                </View>
              )}
            </View>
          </View>

          {/* App Info */}
          <View className="bg-gray-100 rounded-xl p-4 mt-4">
            <Text className="text-center text-gray-600 text-sm">
              OnRoad Real Estate Platform
            </Text>
            <Text className="text-center text-gray-500 text-xs mt-1">
              Version 1.0.0 • {user ? 'Authenticated' : 'Not authenticated'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DashboardScreen;
