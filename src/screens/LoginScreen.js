import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import authService from '../services/auth';
import apiService from '../api';
import { runComprehensiveTests, formatTestResults } from '../utils/networkTest';
import { runQuickTests, formatQuickResults } from '../utils/quickTest';

const LoginSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
});

const LoginScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleNetworkTest = async () => {
    try {
      setIsLoading(true);
      console.log('Running network diagnostics...');

      const testResults = await runComprehensiveTests();
      const formattedResults = formatTestResults(testResults);

      Alert.alert(
        'Network Diagnostics',
        formattedResults,
        [
          { text: 'OK' },
          {
            text: 'Copy Results',
            onPress: () => {
              // In a real app, you might use Clipboard API here
              console.log('Test results:', formattedResults);
              Alert.alert('Results Copied', 'Test results have been logged to console');
            }
          }
        ]
      );
    } catch (error) {
      console.error('Network test error:', error);
      Alert.alert(
        'Test Failed',
        `Failed to run network diagnostics: ${error.message}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async (values) => {
    setIsLoading(true);

    try {
      console.log('Login attempt:', values.email);

      // Quick network test before attempting authentication
      try {
        console.log('Running quick network test...');
        const quickTest = await runQuickTests();

        if (!quickTest.overallSuccess) {
          console.warn('Quick tests failed:', quickTest);

          // Show network issues but allow user to continue
          const quickResults = formatQuickResults(quickTest);
          Alert.alert(
            'Network Issues Detected',
            `${quickResults}\n\nAuthentication may be slower than usual. Continue anyway?`,
            [
              { text: 'Cancel', style: 'cancel', onPress: () => setIsLoading(false) },
              { text: 'Continue', onPress: () => proceedWithLogin(values) }
            ]
          );
          return;
        } else {
          console.log('Quick tests passed, proceeding with authentication');
        }
      } catch (networkTestError) {
        console.warn('Network test failed:', networkTestError.message);
        // Continue with login even if network test fails
      }

      await proceedWithLogin(values);
    } catch (error) {
      console.error('Login process error:', error);
      setIsLoading(false);
    }
  };

  const proceedWithLogin = async (values) => {
    try {
      // Attempt to sign in with Supabase
      const result = await authService.signIn(values.email, values.password);

      if (result.success) {
        console.log('Authentication successful:', result.user.email);

        // Check if this is the user's first sign-in
        try {
          await apiService.firstSignIn();
          console.log('First sign-in API call successful');
        } catch (firstSignInError) {
          // First sign-in API call failed, but user is authenticated
          console.log('First sign-in API call failed:', firstSignInError.message);
          // Continue anyway as the user is authenticated
        }

        Alert.alert(
          'Success',
          'Login successful!',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to Dashboard after successful login
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Dashboard' }],
                });
                console.log('User authenticated, navigating to Dashboard');
              }
            }
          ]
        );
      } else {
        // Enhanced error handling with specific messages and troubleshooting
        const errorType = result.errorType || 'Unknown';
        const originalError = result.originalError || result.error;

        console.error('Login failed:', {
          error: result.error,
          originalError: originalError,
          errorType: errorType
        });

        // Provide specific error messages and troubleshooting steps
        let alertTitle = 'Login Failed';
        let alertMessage = result.error || 'Login failed. Please try again.';
        let alertButtons = [{ text: 'OK' }];

        if (errorType === 'AuthRetryableFetchError' || originalError?.includes('timeout')) {
          alertTitle = 'Network Timeout';
          alertMessage = 'The login request timed out. This could be due to:\n\n• Slow internet connection\n• Server overload\n• Network connectivity issues\n\nPlease check your connection and try again.';
          alertButtons = [
            { text: 'Retry', onPress: () => handleLogin(values) },
            { text: 'Cancel', style: 'cancel' }
          ];
        } else if (originalError?.includes('Network request failed')) {
          alertTitle = 'Network Error';
          alertMessage = 'Unable to connect to the authentication server. Please:\n\n• Check your internet connection\n• Try again in a few moments\n• Contact support if the problem persists';
          alertButtons = [
            { text: 'Retry', onPress: () => handleLogin(values) },
            { text: 'Cancel', style: 'cancel' }
          ];
        } else if (originalError?.includes('Invalid login credentials')) {
          alertTitle = 'Invalid Credentials';
          alertMessage = 'The email or password you entered is incorrect. Please check your credentials and try again.';
        } else if (originalError?.includes('Email not confirmed')) {
          alertTitle = 'Email Not Verified';
          alertMessage = 'Please check your email and click the verification link before signing in.';
        }

        Alert.alert(alertTitle, alertMessage, alertButtons);
      }
    } catch (error) {
      console.error('Unexpected login error:', error);

      // Handle unexpected errors with detailed information
      let errorMessage = 'An unexpected error occurred during login.';
      let troubleshootingSteps = '';

      if (error.message?.includes('timeout') || error.name === 'AuthRetryableFetchError') {
        errorMessage = 'Login request timed out.';
        troubleshootingSteps = '\n\nTroubleshooting steps:\n• Check your internet connection\n• Try again with a stable network\n• Contact support if the issue persists';
      } else if (error.message?.includes('Network')) {
        errorMessage = 'Network connection error.';
        troubleshootingSteps = '\n\nTroubleshooting steps:\n• Verify internet connectivity\n• Check if the server is accessible\n• Try again later';
      }

      Alert.alert(
        'Login Error',
        errorMessage + troubleshootingSteps,
        [
          { text: 'OK' },
          { text: 'View Details', onPress: () => {
            Alert.alert(
              'Error Details',
              `Error Type: ${error.name || 'Unknown'}\nMessage: ${error.message}\n\nIf this problem persists, please contact support with these details.`
            );
          }}
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="flex-1 px-6 pt-15 pb-10">
            {/* App Logo/Title */}
            <View className="items-center mb-15">
              <Text className="text-4xl font-bold text-gray-800 mb-2">OnRoad</Text>
              <Text className="text-base text-gray-500 text-center">Real Estate Transactions Made Simple</Text>
            </View>

            {/* Login Form */}
            <View className="justify-center">
              <Text className="text-3xl font-bold text-gray-800 mb-2 text-center">Welcome Back</Text>
              <Text className="text-base text-gray-500 text-center mb-10">Sign in to your account</Text>

              <Formik
                initialValues={{ email: '', password: '' }}
                validationSchema={LoginSchema}
                onSubmit={handleLogin}
              >
                {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                  <>
                    <View className="mb-5">
                      <Text className="text-base font-semibold text-gray-800 mb-2">Email</Text>
                      <TextInput
                        className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                          errors.email && touched.email ? 'border-red-500' : 'border-gray-200'
                        }`}
                        placeholder="Enter your email"
                        autoCapitalize="none"
                        keyboardType="email-address"
                        autoComplete="email"
                        onChangeText={handleChange('email')}
                        onBlur={handleBlur('email')}
                        value={values.email}
                        editable={!isLoading}
                      />
                      {errors.email && touched.email && (
                        <Text className="text-red-500 text-sm mt-1 ml-1">{errors.email}</Text>
                      )}
                    </View>

                    <View className="mb-5">
                      <Text className="text-base font-semibold text-gray-800 mb-2">Password</Text>
                      <TextInput
                        className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                          errors.password && touched.password ? 'border-red-500' : 'border-gray-200'
                        }`}
                        placeholder="Enter your password"
                        secureTextEntry
                        autoComplete="password"
                        onChangeText={handleChange('password')}
                        onBlur={handleBlur('password')}
                        value={values.password}
                        editable={!isLoading}
                      />
                      {errors.password && touched.password && (
                        <Text className="text-red-500 text-sm mt-1 ml-1">{errors.password}</Text>
                      )}
                    </View>

                    <TouchableOpacity
                      className={`rounded-xl p-4 items-center mt-5 ${
                        isLoading ? 'bg-gray-400' : 'bg-black shadow-lg'
                      }`}
                      onPress={handleSubmit}
                      disabled={isLoading}
                    >
                      <Text className="text-white text-lg font-semibold">
                        {isLoading ? 'Signing In...' : 'Sign In'}
                      </Text>
                    </TouchableOpacity>
                </>
              )}
            </Formik>
          </View>

            {/* Footer Links */}
            <View className="items-center pt-5">
              <TouchableOpacity onPress={() => navigation?.reset({
                index: 0,
                routes: [{ name: 'ForgotPassword' }],
              })}>
                <Text className="text-blue-500 text-base font-medium mb-5">Forgot Password?</Text>
              </TouchableOpacity>

              <View className="flex-row items-center mb-5">
                <Text className="text-base text-gray-500">Don't have an account? </Text>
                <TouchableOpacity onPress={() => navigation?.reset({
                  index: 0,
                  routes: [{ name: 'SignUp' }],
                })}>
                  <Text className="text-base text-blue-500 font-semibold">Sign Up</Text>
                </TouchableOpacity>
              </View>

              {/* Network Diagnostics Button */}
              <TouchableOpacity
                onPress={handleNetworkTest}
                disabled={isLoading}
                className="bg-gray-200 px-4 py-2 rounded-lg"
              >
                <Text className="text-gray-700 text-sm font-medium">
                  {isLoading ? 'Running Tests...' : 'Network Diagnostics'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
