import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import authService from '../services/auth';
import apiService from '../api';

const LoginSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
});

const LoginScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async (values) => {
    setIsLoading(true);

    try {
      console.log('Login attempt:', values.email);

      // Attempt to sign in with Supabase
      const result = await authService.signIn(values.email, values.password);

      if (result.success) {
        console.log('Authentication successful:', result.user.email);

        // Check if this is the user's first sign-in
        try {
          await apiService.firstSignIn();
          console.log('First sign-in API call successful');
        } catch (firstSignInError) {
          // First sign-in API call failed, but user is authenticated
          console.log('First sign-in API call failed:', firstSignInError.message);
          // Continue anyway as the user is authenticated
        }

        Alert.alert(
          'Success',
          'Login successful!',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to Dashboard after successful login
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Dashboard' }],
                });
                console.log('User authenticated, navigating to Dashboard');
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Login failed. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="flex-1 px-6 pt-15 pb-10">
            {/* App Logo/Title */}
            <View className="items-center mb-15">
              <Text className="text-4xl font-bold text-gray-800 mb-2">OnRoad</Text>
              <Text className="text-base text-gray-500 text-center">Real Estate Transactions Made Simple</Text>
            </View>

            {/* Login Form */}
            <View className="justify-center">
              <Text className="text-3xl font-bold text-gray-800 mb-2 text-center">Welcome Back</Text>
              <Text className="text-base text-gray-500 text-center mb-10">Sign in to your account</Text>

              <Formik
                initialValues={{ email: '', password: '' }}
                validationSchema={LoginSchema}
                onSubmit={handleLogin}
              >
                {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                  <>
                    <View className="mb-5">
                      <Text className="text-base font-semibold text-gray-800 mb-2">Email</Text>
                      <TextInput
                        className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                          errors.email && touched.email ? 'border-red-500' : 'border-gray-200'
                        }`}
                        placeholder="Enter your email"
                        autoCapitalize="none"
                        keyboardType="email-address"
                        autoComplete="email"
                        onChangeText={handleChange('email')}
                        onBlur={handleBlur('email')}
                        value={values.email}
                        editable={!isLoading}
                      />
                      {errors.email && touched.email && (
                        <Text className="text-red-500 text-sm mt-1 ml-1">{errors.email}</Text>
                      )}
                    </View>

                    <View className="mb-5">
                      <Text className="text-base font-semibold text-gray-800 mb-2">Password</Text>
                      <TextInput
                        className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                          errors.password && touched.password ? 'border-red-500' : 'border-gray-200'
                        }`}
                        placeholder="Enter your password"
                        secureTextEntry
                        autoComplete="password"
                        onChangeText={handleChange('password')}
                        onBlur={handleBlur('password')}
                        value={values.password}
                        editable={!isLoading}
                      />
                      {errors.password && touched.password && (
                        <Text className="text-red-500 text-sm mt-1 ml-1">{errors.password}</Text>
                      )}
                    </View>

                    <TouchableOpacity
                      className={`rounded-xl p-4 items-center mt-5 ${
                        isLoading ? 'bg-gray-400' : 'bg-black shadow-lg'
                      }`}
                      onPress={handleSubmit}
                      disabled={isLoading}
                    >
                      <Text className="text-white text-lg font-semibold">
                        {isLoading ? 'Signing In...' : 'Sign In'}
                      </Text>
                    </TouchableOpacity>
                </>
              )}
            </Formik>
          </View>

            {/* Footer Links */}
            <View className="items-center pt-5">
              <TouchableOpacity onPress={() => navigation?.reset({
                index: 0,
                routes: [{ name: 'ForgotPassword' }],
              })}>
                <Text className="text-blue-500 text-base font-medium mb-5">Forgot Password?</Text>
              </TouchableOpacity>

              <View className="flex-row items-center">
                <Text className="text-base text-gray-500">Don't have an account? </Text>
                <TouchableOpacity onPress={() => navigation?.reset({
                  index: 0,
                  routes: [{ name: 'SignUp' }],
                })}>
                  <Text className="text-base text-blue-500 font-semibold">Sign Up</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
