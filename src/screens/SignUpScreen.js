import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import authService from '../services/auth';

const SignUpSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().min(8, 'Password must be at least 8 characters').required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm your password'),
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
});

const SignUpScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSignUp = async (values) => {
    setIsLoading(true);
    
    try {
      console.log('Sign up attempt:', values.email);
      
      // Attempt to sign up with Supabase
      const result = await authService.signUp(values.email, values.password, {
        first_name: values.firstName,
        last_name: values.lastName,
      });
      
      if (result.success) {
        Alert.alert(
          'Success', 
          result.message || 'Account created successfully! Please check your email for verification.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate back to login screen
                navigation.navigate('Login');
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Sign up failed. Please try again.');
      }
    } catch (error) {
      console.error('Sign up error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView 
          className="flex-1" 
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="flex-1 px-6 pt-15 pb-10">
            {/* App Logo/Title */}
            <View className="items-center mb-10">
              <Text className="text-4xl font-bold text-gray-800 mb-2">OnRoad</Text>
              <Text className="text-base text-gray-500 text-center">Real Estate Transactions Made Simple</Text>
            </View>

            {/* Sign Up Form */}
            <View className="justify-center">
              <Text className="text-3xl font-bold text-gray-800 mb-2 text-center">Create Account</Text>
              <Text className="text-base text-gray-500 text-center mb-8">Join OnRoad today</Text>

              <Formik
                initialValues={{ 
                  email: '', 
                  password: '', 
                  confirmPassword: '',
                  firstName: '',
                  lastName: ''
                }}
                validationSchema={SignUpSchema}
                onSubmit={handleSignUp}
              >
                {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                  <>
                    <View className="flex-row mb-5">
                      <View className="flex-1 mr-2">
                        <Text className="text-base font-semibold text-gray-800 mb-2">First Name</Text>
                        <TextInput
                          className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                            errors.firstName && touched.firstName ? 'border-red-500' : 'border-gray-200'
                          }`}
                          placeholder="First name"
                          autoCapitalize="words"
                          onChangeText={handleChange('firstName')}
                          onBlur={handleBlur('firstName')}
                          value={values.firstName}
                          editable={!isLoading}
                        />
                        {errors.firstName && touched.firstName && (
                          <Text className="text-red-500 text-sm mt-1 ml-1">{errors.firstName}</Text>
                        )}
                      </View>
                      
                      <View className="flex-1 ml-2">
                        <Text className="text-base font-semibold text-gray-800 mb-2">Last Name</Text>
                        <TextInput
                          className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                            errors.lastName && touched.lastName ? 'border-red-500' : 'border-gray-200'
                          }`}
                          placeholder="Last name"
                          autoCapitalize="words"
                          onChangeText={handleChange('lastName')}
                          onBlur={handleBlur('lastName')}
                          value={values.lastName}
                          editable={!isLoading}
                        />
                        {errors.lastName && touched.lastName && (
                          <Text className="text-red-500 text-sm mt-1 ml-1">{errors.lastName}</Text>
                        )}
                      </View>
                    </View>

                    <View className="mb-5">
                      <Text className="text-base font-semibold text-gray-800 mb-2">Email</Text>
                      <TextInput
                        className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                          errors.email && touched.email ? 'border-red-500' : 'border-gray-200'
                        }`}
                        placeholder="Enter your email"
                        autoCapitalize="none"
                        keyboardType="email-address"
                        autoComplete="email"
                        onChangeText={handleChange('email')}
                        onBlur={handleBlur('email')}
                        value={values.email}
                        editable={!isLoading}
                      />
                      {errors.email && touched.email && (
                        <Text className="text-red-500 text-sm mt-1 ml-1">{errors.email}</Text>
                      )}
                    </View>

                    <View className="mb-5">
                      <Text className="text-base font-semibold text-gray-800 mb-2">Password</Text>
                      <TextInput
                        className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                          errors.password && touched.password ? 'border-red-500' : 'border-gray-200'
                        }`}
                        placeholder="Create a password"
                        secureTextEntry
                        autoComplete="new-password"
                        onChangeText={handleChange('password')}
                        onBlur={handleBlur('password')}
                        value={values.password}
                        editable={!isLoading}
                      />
                      {errors.password && touched.password && (
                        <Text className="text-red-500 text-sm mt-1 ml-1">{errors.password}</Text>
                      )}
                    </View>

                    <View className="mb-5">
                      <Text className="text-base font-semibold text-gray-800 mb-2">Confirm Password</Text>
                      <TextInput
                        className={`border rounded-xl p-4 text-base bg-white text-gray-800 ${
                          errors.confirmPassword && touched.confirmPassword ? 'border-red-500' : 'border-gray-200'
                        }`}
                        placeholder="Confirm your password"
                        secureTextEntry
                        autoComplete="new-password"
                        onChangeText={handleChange('confirmPassword')}
                        onBlur={handleBlur('confirmPassword')}
                        value={values.confirmPassword}
                        editable={!isLoading}
                      />
                      {errors.confirmPassword && touched.confirmPassword && (
                        <Text className="text-red-500 text-sm mt-1 ml-1">{errors.confirmPassword}</Text>
                      )}
                    </View>

                    <TouchableOpacity
                      className={`rounded-xl p-4 items-center mt-5 ${
                        isLoading ? 'bg-gray-400' : 'bg-black shadow-lg'
                      }`}
                      onPress={handleSubmit}
                      disabled={isLoading}
                    >
                      <Text className="text-white text-lg font-semibold">
                        {isLoading ? 'Creating Account...' : 'Create Account'}
                      </Text>
                    </TouchableOpacity>
                </>
              )}
            </Formik>
          </View>

            {/* Footer Links */}
            <View className="items-center pt-5">
              <View className="flex-row items-center">
                <Text className="text-base text-gray-500">Already have an account? </Text>
                <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                  <Text className="text-base text-blue-500 font-semibold">Sign In</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignUpScreen;
