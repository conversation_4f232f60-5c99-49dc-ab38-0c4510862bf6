/**
 * API Configuration
 * Manages API base URLs and configuration for different environments
 */

import Constants from 'expo-constants';

// Get configuration from Expo Constants
const getApiConfig = () => {
  const extra = Constants.expoConfig?.extra || Constants.manifest?.extra || {};

  // Environment detection
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isTest = process.env.NODE_ENV === 'test';

  return {
    baseUrl: extra.apiBaseUrl || process.env.API_BASE_URL || 'https://onroad-express-3d680c74f3cc.herokuapp.com/api',
    timeout: extra.apiTimeout || parseInt(process.env.API_TIMEOUT) || 30000,
    debugMode: extra.debugMode || process.env.DEBUG_MODE === 'true' || __DEV__,
    isDevelopment,
    isTest
  };
};

const apiConfig = getApiConfig();

// API Configuration
export const API_CONFIG = {
  // Base URL from configuration
  BASE_URL: apiConfig.baseUrl,

  // Request timeout
  TIMEOUT: apiConfig.timeout,

  // API version
  VERSION: 'v1',

  // Request headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },

  // Retry configuration
  RETRY_CONFIG: {
    maxRetries: 3,
    retryDelay: 1000,
    retryOn: [408, 429, 500, 502, 503, 504],
  },

  // Debug mode
  DEBUG: apiConfig.debugMode,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  FIRST_SIGNIN: '/firstSignin',
  
  // Profiles
  PROFILES: '/profiles',
  
  // Deals
  DEALS: '/deals',
  DEAL_BY_ID: (id) => `/deals/${id}`,
  
  // Properties
  PROPERTIES: (dealId) => `/dealOptions/${dealId}/properties`,
  PROPERTY_BY_ID: (propertyId) => `/dealOptions/properties/${propertyId}`,
  
  // Forms
  FORMS_BY_PROPERTY: (propertyId) => `/forms/property/${propertyId}`,
  FORM_BY_ID: (formId) => `/forms/${formId}`,
  
  // Client Intake
  CLIENT_INTAKE_SCHEMA: (searchId) => `/client/intake/schema/${searchId}`,
  CLIENT_INTAKE_ANSWERS: (searchId) => `/client/intake/answers/get/${searchId}`,
  REALTOR_CLIENT_INTAKE: (dealId) => `/realtor/client-intake/get-by-dealid/${dealId}`,
  PUBLISH_INTAKE_TEMPLATE: (dealId) => `/realtor/client-intake/push-schema-to-tether/${dealId}`,
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
};

// Error messages
export const API_ERRORS = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  TIMEOUT_ERROR: 'Request timeout. Please try again.',
  UNAUTHORIZED: 'Authentication required. Please log in.',
  FORBIDDEN: 'Access denied. Insufficient permissions.',
  NOT_FOUND: 'Resource not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
};

export default API_CONFIG;
