/**
 * Supabase Configuration
 * Manages Supabase client configuration and initialization
 */

import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Supabase Configuration
export const SUPABASE_CONFIG = {
  // Supabase project URL - Replace with your actual Supabase project URL
  URL: process.env.SUPABASE_URL || 'https://your-project-id.supabase.co',

  // Supabase anon/public key - Replace with your actual anon key
  ANON_KEY: process.env.SUPABASE_ANON_KEY || 'your-anon-key-here',

  // Auth configuration
  AUTH: {
    // Auto refresh token
    autoRefreshToken: true,

    // Persist session
    persistSession: true,

    // Detect session in URL (for web)
    detectSessionInUrl: false,

    // Storage configuration for React Native
    storage: AsyncStorage,

    // Flow type for auth
    flowType: 'pkce',

    // Network timeout configuration (in milliseconds)
    // Increase timeout for slower networks
    timeout: parseInt(process.env.SUPABASE_TIMEOUT) || 30000, // 30 seconds

    // Retry configuration for network failures
    retryAttempts: parseInt(process.env.SUPABASE_RETRY_ATTEMPTS) || 3,
    retryDelay: parseInt(process.env.SUPABASE_RETRY_DELAY) || 1000, // 1 second
  },

  // Database configuration
  DB: {
    schema: 'public',
  },

  // Real-time configuration
  REALTIME: {
    params: {
      eventsPerSecond: 10,
    },
  },

  // Global fetch configuration for better network handling
  GLOBAL: {
    fetch: (url, options = {}) => {
      // Add timeout to all requests
      const timeoutMs = SUPABASE_CONFIG.AUTH.timeout;

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

      // Enhanced fetch with timeout and better error handling
      return fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      }).then(response => {
        clearTimeout(timeoutId);
        return response;
      }).catch(error => {
        clearTimeout(timeoutId);

        // Enhanced error information
        if (error.name === 'AbortError') {
          const timeoutError = new Error(`Network request timed out after ${timeoutMs}ms`);
          timeoutError.name = 'AuthRetryableFetchError';
          timeoutError.isTimeout = true;
          throw timeoutError;
        }

        // Add more context to network errors
        if (error.message.includes('Network request failed')) {
          error.name = 'AuthRetryableFetchError';
          error.isNetworkError = true;
        }

        throw error;
      });
    }
  }
};

// Create Supabase client with enhanced configuration
export const supabase = createClient(
  SUPABASE_CONFIG.URL,
  SUPABASE_CONFIG.ANON_KEY,
  {
    auth: SUPABASE_CONFIG.AUTH,
    db: SUPABASE_CONFIG.DB,
    realtime: SUPABASE_CONFIG.REALTIME,
    global: {
      fetch: SUPABASE_CONFIG.GLOBAL.fetch,
    },
  }
);

// Configuration validation and logging
const validateSupabaseConfig = () => {
  const issues = [];

  if (!SUPABASE_CONFIG.URL || SUPABASE_CONFIG.URL.includes('your-project-id')) {
    issues.push('SUPABASE_URL is not configured properly');
  }

  if (!SUPABASE_CONFIG.ANON_KEY || SUPABASE_CONFIG.ANON_KEY.includes('your-anon-key')) {
    issues.push('SUPABASE_ANON_KEY is not configured properly');
  }

  if (!SUPABASE_CONFIG.URL.startsWith('https://')) {
    issues.push('SUPABASE_URL should use HTTPS');
  }

  if (SUPABASE_CONFIG.ANON_KEY && SUPABASE_CONFIG.ANON_KEY.length < 100) {
    issues.push('SUPABASE_ANON_KEY appears to be too short (should be ~100+ characters)');
  }

  return issues;
};

// Log configuration status
if (process.env.DEBUG_MODE === 'true' || __DEV__) {
  const configIssues = validateSupabaseConfig();

  console.log('[SUPABASE-CONFIG] Configuration status:', {
    url: SUPABASE_CONFIG.URL,
    hasAnonKey: !!SUPABASE_CONFIG.ANON_KEY,
    anonKeyLength: SUPABASE_CONFIG.ANON_KEY?.length,
    timeout: SUPABASE_CONFIG.AUTH.timeout,
    retryAttempts: SUPABASE_CONFIG.AUTH.retryAttempts,
    issues: configIssues
  });

  if (configIssues.length > 0) {
    console.warn('[SUPABASE-CONFIG] Configuration issues found:', configIssues);
  }
}

// Auth event types
export const AUTH_EVENTS = {
  SIGNED_IN: 'SIGNED_IN',
  SIGNED_OUT: 'SIGNED_OUT',
  TOKEN_REFRESHED: 'TOKEN_REFRESHED',
  USER_UPDATED: 'USER_UPDATED',
  PASSWORD_RECOVERY: 'PASSWORD_RECOVERY',
};

// User roles (based on the API spec Profile schema)
export const USER_ROLES = {
  BROKER: 'broker',
  REALTOR: 'realtor',
  CLIENT: 'client',
};

// Auth storage keys
export const AUTH_STORAGE_KEYS = {
  SESSION: 'supabase.auth.token',
  USER: 'supabase.auth.user',
  REFRESH_TOKEN: 'supabase.auth.refresh_token',
};

export default supabase;
