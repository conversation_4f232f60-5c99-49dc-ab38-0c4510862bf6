/**
 * Supabase Configuration
 * Manages Supabase client configuration and initialization
 */

import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Supabase Configuration
export const SUPABASE_CONFIG = {
  // Supabase project URL - Replace with your actual Supabase project URL
  URL: process.env.SUPABASE_URL || 'https://your-project-id.supabase.co',
  
  // Supabase anon/public key - Replace with your actual anon key
  ANON_KEY: process.env.SUPABASE_ANON_KEY || 'your-anon-key-here',
  
  // Auth configuration
  AUTH: {
    // Auto refresh token
    autoRefreshToken: true,
    
    // Persist session
    persistSession: true,
    
    // Detect session in URL (for web)
    detectSessionInUrl: false,
    
    // Storage configuration for React Native
    storage: AsyncStorage,
    
    // Flow type for auth
    flowType: 'pkce',
  },
  
  // Database configuration
  DB: {
    schema: 'public',
  },
  
  // Real-time configuration
  REALTIME: {
    params: {
      eventsPerSecond: 10,
    },
  },
};

// Create Supabase client
export const supabase = createClient(
  SUPABASE_CONFIG.URL,
  SUPABASE_CONFIG.ANON_KEY,
  {
    auth: SUPABASE_CONFIG.AUTH,
    db: SUPABASE_CONFIG.DB,
    realtime: SUPABASE_CONFIG.REALTIME,
  }
);

// Auth event types
export const AUTH_EVENTS = {
  SIGNED_IN: 'SIGNED_IN',
  SIGNED_OUT: 'SIGNED_OUT',
  TOKEN_REFRESHED: 'TOKEN_REFRESHED',
  USER_UPDATED: 'USER_UPDATED',
  PASSWORD_RECOVERY: 'PASSWORD_RECOVERY',
};

// User roles (based on the API spec Profile schema)
export const USER_ROLES = {
  BROKER: 'broker',
  REALTOR: 'realtor',
  CLIENT: 'client',
};

// Auth storage keys
export const AUTH_STORAGE_KEYS = {
  SESSION: 'supabase.auth.token',
  USER: 'supabase.auth.user',
  REFRESH_TOKEN: 'supabase.auth.refresh_token',
};

export default supabase;
