/**
 * Supabase Configuration
 * Manages Supabase client configuration and initialization
 */

import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

// Get configuration from environment variables and Expo Constants
const getConfig = () => {
  const extra = Constants.expoConfig?.extra || Constants.manifest?.extra || {};

  // Debug log to see what's available
  if (__DEV__) {
    console.log('[CONFIG] Available configuration sources:', {
      hasExpoConfig: !!Constants.expoConfig,
      hasManifest: !!Constants.manifest,
      hasExtra: !!extra,
      extraKeys: Object.keys(extra),
      processEnv: {
        hasSupabaseUrl: !!process.env.SUPABASE_URL,
        hasSupabaseKey: !!process.env.SUPABASE_ANON_KEY,
        supabaseUrlValue: process.env.SUPABASE_URL ? `${process.env.SUPABASE_URL.substring(0, 20)}...` : 'NOT_SET',
        supabaseKeyValue: process.env.SUPABASE_ANON_KEY ? `${process.env.SUPABASE_ANON_KEY.substring(0, 10)}...` : 'NOT_SET',
        nodeEnv: process.env.NODE_ENV
      }
    });
  }

  // Use process.env directly since it's being loaded by Expo
  const config = {
    supabaseUrl: process.env.SUPABASE_URL || extra.supabaseUrl || 'https://your-project-id.supabase.co',
    supabaseAnonKey: process.env.SUPABASE_ANON_KEY || extra.supabaseAnonKey || 'your-anon-key-here',
    supabaseJwtSecret: process.env.SUPABASE_JWT_SECRET || extra.supabaseJwtSecret || '',
    apiBaseUrl: process.env.API_BASE_URL || extra.apiBaseUrl || 'http://localhost:3000/api',
    apiTimeout: parseInt(process.env.API_TIMEOUT) || extra.apiTimeout || 30000,
    supabaseTimeout: parseInt(process.env.SUPABASE_TIMEOUT) || extra.supabaseTimeout || 60000,
    supabaseRetryAttempts: parseInt(process.env.SUPABASE_RETRY_ATTEMPTS) || extra.supabaseRetryAttempts || 5,
    supabaseRetryDelay: parseInt(process.env.SUPABASE_RETRY_DELAY) || extra.supabaseRetryDelay || 2000,
    useCustomFetch: process.env.USE_CUSTOM_FETCH === 'true' || extra.useCustomFetch || false,
    debugMode: process.env.DEBUG_MODE === 'true' || extra.debugMode || __DEV__
  };

  // Debug log the final configuration
  if (__DEV__) {
    console.log('[CONFIG] Final configuration:', {
      supabaseUrl: config.supabaseUrl,
      hasAnonKey: !!config.supabaseAnonKey,
      anonKeyLength: config.supabaseAnonKey?.length,
      timeout: config.supabaseTimeout,
      retryAttempts: config.supabaseRetryAttempts,
      debugMode: config.debugMode,
      useCustomFetch: config.useCustomFetch
    });
  }

  return config;
};

const config = getConfig();

// Supabase Configuration using the loaded config
export const SUPABASE_CONFIG = {
  // Supabase project URL from configuration
  URL: config.supabaseUrl,

  // Supabase anon/public key from configuration
  ANON_KEY: config.supabaseAnonKey,

  // Auth configuration
  AUTH: {
    // Auto refresh token
    autoRefreshToken: true,

    // Persist session
    persistSession: true,

    // Detect session in URL (for web)
    detectSessionInUrl: false,

    // Storage configuration for React Native
    storage: AsyncStorage,

    // Flow type for auth
    flowType: 'pkce',

    // Network timeout configuration (in milliseconds)
    timeout: config.supabaseTimeout,

    // Retry configuration for network failures
    retryAttempts: config.supabaseRetryAttempts,
    retryDelay: config.supabaseRetryDelay,
  },

  // Database configuration
  DB: {
    schema: 'public',
  },

  // Real-time configuration
  REALTIME: {
    params: {
      eventsPerSecond: 10,
    },
  },

  // Global fetch configuration for better network handling
  GLOBAL: {
    fetch: (url, options = {}) => {
      // Get timeout from config
      const timeoutMs = config.supabaseTimeout;

      console.log(`[FETCH] Making request to ${url} with timeout ${timeoutMs}ms`);

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log(`[FETCH] Request to ${url} timed out after ${timeoutMs}ms`);
        controller.abort();
      }, timeoutMs);

      // Enhanced fetch with timeout and better error handling
      return fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      }).then(response => {
        clearTimeout(timeoutId);
        console.log(`[FETCH] Request to ${url} completed with status ${response.status}`);
        return response;
      }).catch(error => {
        clearTimeout(timeoutId);

        console.log(`[FETCH] Request to ${url} failed:`, error.message);

        // Enhanced error information
        if (error.name === 'AbortError') {
          const timeoutError = new Error(`Network request timed out after ${timeoutMs}ms`);
          timeoutError.name = 'AuthRetryableFetchError';
          timeoutError.isTimeout = true;
          timeoutError.originalUrl = url;
          console.error(`[FETCH] Timeout error for ${url}:`, timeoutError);
          throw timeoutError;
        }

        // Add more context to network errors
        if (error.message.includes('Network request failed')) {
          error.name = 'AuthRetryableFetchError';
          error.isNetworkError = true;
          error.originalUrl = url;
        }

        throw error;
      });
    }
  }
};

// Create Supabase client with enhanced configuration
// Try without custom fetch first to isolate the issue
const useCustomFetch = config.useCustomFetch;

export const supabase = createClient(
  SUPABASE_CONFIG.URL,
  SUPABASE_CONFIG.ANON_KEY,
  {
    auth: {
      ...SUPABASE_CONFIG.AUTH,
      // Remove custom timeout from auth config to avoid conflicts
      timeout: undefined,
      retryAttempts: undefined,
      retryDelay: undefined,
    },
    db: SUPABASE_CONFIG.DB,
    realtime: SUPABASE_CONFIG.REALTIME,
    ...(useCustomFetch && {
      global: {
        fetch: SUPABASE_CONFIG.GLOBAL.fetch,
      },
    }),
  }
);

// Configuration validation and logging
const validateSupabaseConfig = () => {
  const issues = [];

  if (!SUPABASE_CONFIG.URL || SUPABASE_CONFIG.URL.includes('your-project-id')) {
    issues.push('SUPABASE_URL is not configured properly');
  }

  if (!SUPABASE_CONFIG.ANON_KEY || SUPABASE_CONFIG.ANON_KEY.includes('your-anon-key')) {
    issues.push('SUPABASE_ANON_KEY is not configured properly');
  }

  if (!SUPABASE_CONFIG.URL.startsWith('https://')) {
    issues.push('SUPABASE_URL should use HTTPS');
  }

  if (SUPABASE_CONFIG.ANON_KEY && SUPABASE_CONFIG.ANON_KEY.length < 100) {
    issues.push('SUPABASE_ANON_KEY appears to be too short (should be ~100+ characters)');
  }

  return issues;
};

// Log configuration status
if (config.debugMode) {
  const configIssues = validateSupabaseConfig();

  console.log('[SUPABASE-CONFIG] Configuration status:', {
    configSource: 'Expo Constants + process.env fallback',
    url: SUPABASE_CONFIG.URL,
    hasAnonKey: !!SUPABASE_CONFIG.ANON_KEY,
    anonKeyLength: SUPABASE_CONFIG.ANON_KEY?.length,
    timeout: SUPABASE_CONFIG.AUTH.timeout,
    retryAttempts: SUPABASE_CONFIG.AUTH.retryAttempts,
    useCustomFetch: useCustomFetch,
    rawConfig: {
      supabaseUrl: config.supabaseUrl,
      hasAnonKey: !!config.supabaseAnonKey,
      anonKeyLength: config.supabaseAnonKey?.length,
      timeout: config.supabaseTimeout,
      debugMode: config.debugMode
    },
    issues: configIssues
  });

  if (configIssues.length > 0) {
    console.warn('[SUPABASE-CONFIG] Configuration issues found:', configIssues);
  } else {
    console.log('[SUPABASE-CONFIG] ✅ Configuration loaded successfully from Expo Constants');
  }
}

// Auth event types
export const AUTH_EVENTS = {
  SIGNED_IN: 'SIGNED_IN',
  SIGNED_OUT: 'SIGNED_OUT',
  TOKEN_REFRESHED: 'TOKEN_REFRESHED',
  USER_UPDATED: 'USER_UPDATED',
  PASSWORD_RECOVERY: 'PASSWORD_RECOVERY',
};

// User roles (based on the API spec Profile schema)
export const USER_ROLES = {
  BROKER: 'broker',
  REALTOR: 'realtor',
  CLIENT: 'client',
};

// Auth storage keys
export const AUTH_STORAGE_KEYS = {
  SESSION: 'supabase.auth.token',
  USER: 'supabase.auth.user',
  REFRESH_TOKEN: 'supabase.auth.refresh_token',
};

export default supabase;
