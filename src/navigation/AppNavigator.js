/**
 * App Navigator
 * Simple navigation setup for demonstrating the authentication flow
 * 
 * Note: This is a basic implementation for testing purposes.
 * In a production app, you would use React Navigation with proper
 * stack navigators, authentication guards, and deep linking.
 */

import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import authService from '../services/auth';
import LoginScreen from '../screens/LoginScreen';
import SignUpScreen from '../screens/SignUpScreen';
import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import DashboardScreen from '../screens/DashboardScreen';

const AppNavigator = () => {
  const [currentScreen, setCurrentScreen] = useState('Login');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check initial authentication state
    checkAuthState();
    
    // Listen for auth state changes
    const handleAuthChange = (event, user) => {
      console.log('Auth state changed in navigator:', event, user?.email);
      setIsAuthenticated(!!user);
      
      if (user && currentScreen !== 'Dashboard') {
        setCurrentScreen('Dashboard');
      } else if (!user && currentScreen === 'Dashboard') {
        setCurrentScreen('Login');
      }
    };

    authService.addAuthListener(handleAuthChange);

    return () => {
      authService.removeAuthListener(handleAuthChange);
    };
  }, [currentScreen]);

  const checkAuthState = async () => {
    try {
      const user = authService.getCurrentUser();
      const authenticated = authService.isAuthenticated();
      
      console.log('Initial auth check:', { user: user?.email, authenticated });
      
      setIsAuthenticated(authenticated);
      if (authenticated) {
        setCurrentScreen('Dashboard');
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const navigation = {
    navigate: (screenName) => {
      console.log('Navigate to:', screenName);
      setCurrentScreen(screenName);
    },
    reset: ({ routes }) => {
      const screenName = routes[0]?.name;
      console.log('Reset navigation to:', screenName);
      setCurrentScreen(screenName);
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <Text className="text-lg text-gray-600">Loading...</Text>
      </View>
    );
  }

  // Render the appropriate screen based on current state
  switch (currentScreen) {
    case 'Login':
      return <LoginScreen navigation={navigation} />;
    case 'SignUp':
      return <SignUpScreen navigation={navigation} />;
    case 'ForgotPassword':
      return <ForgotPasswordScreen navigation={navigation} />;
    case 'Dashboard':
      return <DashboardScreen navigation={navigation} />;
    default:
      return <LoginScreen navigation={navigation} />;
  }
};

export default AppNavigator;
