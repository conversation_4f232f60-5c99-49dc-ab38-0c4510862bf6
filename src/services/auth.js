/**
 * Authentication Service
 * Handles all authentication-related operations using Supabase
 */

import { supabase, AUTH_EVENTS, USER_ROLES, SUPABASE_CONFIG } from '../config/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Debug logging utility
const debugLog = (category, message, data = null) => {
  if (process.env.DEBUG_MODE === 'true' || __DEV__) {
    const timestamp = new Date().toISOString();
    console.log(`[AUTH-${category}] ${timestamp}: ${message}`);
    if (data) {
      console.log(`[AUTH-${category}] Data:`, data);
    }
  }
};

// Network timing utility
const withTiming = async (operation, operationName) => {
  const startTime = Date.now();
  debugLog('TIMING', `Starting ${operationName}`);

  try {
    const result = await operation();
    const duration = Date.now() - startTime;
    debugLog('TIMING', `${operationName} completed in ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    debugLog('TIMING', `${operationName} failed after ${duration}ms`, { error: error.message });
    throw error;
  }
};

class AuthService {
  constructor() {
    this.currentUser = null;
    this.currentSession = null;
    this.authListeners = [];

    // Log Supabase configuration for debugging
    debugLog('CONFIG', 'Initializing AuthService with Supabase configuration', {
      url: SUPABASE_CONFIG.URL,
      hasAnonKey: !!SUPABASE_CONFIG.ANON_KEY,
      anonKeyLength: SUPABASE_CONFIG.ANON_KEY?.length,
      authConfig: SUPABASE_CONFIG.AUTH,
      flowType: SUPABASE_CONFIG.AUTH.flowType,
      autoRefreshToken: SUPABASE_CONFIG.AUTH.autoRefreshToken,
      persistSession: SUPABASE_CONFIG.AUTH.persistSession
    });

    // Initialize auth state
    this.initializeAuth();
  }

  /**
   * Initialize authentication state
   */
  async initializeAuth() {
    try {
      debugLog('INIT', 'Starting authentication initialization');

      // Get current session with timing
      const sessionResult = await withTiming(
        () => supabase.auth.getSession(),
        'getSession'
      );

      const { data: { session }, error } = sessionResult;

      if (error) {
        debugLog('ERROR', 'Error getting session during initialization', {
          error: error.message,
          errorCode: error.status,
          errorDetails: error
        });
        console.error('Error getting session:', error);
        return;
      }

      if (session) {
        debugLog('INIT', 'Found existing session', {
          userId: session.user?.id,
          email: session.user?.email,
          expiresAt: session.expires_at,
          tokenType: session.token_type,
          hasAccessToken: !!session.access_token,
          hasRefreshToken: !!session.refresh_token
        });

        this.currentSession = session;
        this.currentUser = session.user;
        this.notifyAuthListeners(AUTH_EVENTS.SIGNED_IN, session.user);
      } else {
        debugLog('INIT', 'No existing session found');
      }

      // Listen for auth changes
      debugLog('INIT', 'Setting up auth state change listener');
      supabase.auth.onAuthStateChange((event, session) => {
        this.handleAuthStateChange(event, session);
      });

      debugLog('INIT', 'Authentication initialization completed successfully');
    } catch (error) {
      debugLog('ERROR', 'Critical error during authentication initialization', {
        error: error.message,
        stack: error.stack,
        name: error.name
      });
      console.error('Error initializing auth:', error);
    }
  }

  /**
   * Handle auth state changes
   */
  handleAuthStateChange(event, session) {
    console.log('Auth state changed:', event, session?.user?.email);
    
    this.currentSession = session;
    this.currentUser = session?.user || null;
    
    switch (event) {
      case 'SIGNED_IN':
        this.notifyAuthListeners(AUTH_EVENTS.SIGNED_IN, session.user);
        break;
      case 'SIGNED_OUT':
        this.notifyAuthListeners(AUTH_EVENTS.SIGNED_OUT, null);
        break;
      case 'TOKEN_REFRESHED':
        this.notifyAuthListeners(AUTH_EVENTS.TOKEN_REFRESHED, session.user);
        break;
      case 'USER_UPDATED':
        this.notifyAuthListeners(AUTH_EVENTS.USER_UPDATED, session.user);
        break;
      case 'PASSWORD_RECOVERY':
        this.notifyAuthListeners(AUTH_EVENTS.PASSWORD_RECOVERY, session.user);
        break;
    }
  }

  /**
   * Sign up a new user
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {Object} metadata - Additional user metadata
   * @returns {Promise} Sign up result
   */
  async signUp(email, password, metadata = {}) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        user: data.user,
        session: data.session,
        message: 'Account created successfully. Please check your email for verification.',
      };
    } catch (error) {
      console.error('Sign up error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Sign in a user
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise} Sign in result
   */
  async signIn(email, password) {
    try {
      debugLog('SIGNIN', 'Starting sign in process', {
        email: email,
        hasPassword: !!password,
        passwordLength: password?.length,
        supabaseUrl: SUPABASE_CONFIG.URL,
        timestamp: new Date().toISOString()
      });

      // Check network connectivity first
      debugLog('SIGNIN', 'Checking Supabase client configuration', {
        clientExists: !!supabase,
        authExists: !!supabase?.auth,
        configUrl: SUPABASE_CONFIG.URL,
        configAnonKey: SUPABASE_CONFIG.ANON_KEY ? `${SUPABASE_CONFIG.ANON_KEY.substring(0, 10)}...` : 'NOT_SET'
      });

      // Perform sign in with detailed timing and error tracking
      const signInResult = await withTiming(
        async () => {
          debugLog('SIGNIN', 'Calling supabase.auth.signInWithPassword');

          const result = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          debugLog('SIGNIN', 'Received response from Supabase', {
            hasData: !!result.data,
            hasError: !!result.error,
            hasUser: !!result.data?.user,
            hasSession: !!result.data?.session,
            errorMessage: result.error?.message,
            errorStatus: result.error?.status,
            errorCode: result.error?.code
          });

          return result;
        },
        'signInWithPassword'
      );

      const { data, error } = signInResult;

      if (error) {
        debugLog('ERROR', 'Sign in failed with Supabase error', {
          errorMessage: error.message,
          errorStatus: error.status,
          errorCode: error.code,
          errorName: error.name,
          errorStack: error.stack,
          isNetworkError: error.name?.includes('Network') || error.message?.includes('Network') || error.message?.includes('timeout'),
          isRetryableError: error.name?.includes('Retryable') || error.message?.includes('retryable'),
          fullError: error
        });
        throw error;
      }

      debugLog('SIGNIN', 'Sign in successful', {
        userId: data.user?.id,
        userEmail: data.user?.email,
        sessionExists: !!data.session,
        accessTokenExists: !!data.session?.access_token,
        refreshTokenExists: !!data.session?.refresh_token,
        expiresAt: data.session?.expires_at
      });

      return {
        success: true,
        user: data.user,
        session: data.session,
        message: 'Signed in successfully',
      };
    } catch (error) {
      debugLog('ERROR', 'Sign in process failed with exception', {
        errorMessage: error.message,
        errorName: error.name,
        errorStack: error.stack,
        isNetworkTimeout: error.message?.includes('timeout') || error.message?.includes('Network request timed out'),
        isAuthRetryableError: error.name === 'AuthRetryableFetchError',
        fullError: error
      });

      console.error('Sign in error:', error);

      // Provide more specific error messages based on error type
      let userFriendlyMessage = error.message;
      if (error.message?.includes('timeout') || error.message?.includes('Network request timed out')) {
        userFriendlyMessage = 'Network timeout. Please check your internet connection and try again.';
      } else if (error.name === 'AuthRetryableFetchError') {
        userFriendlyMessage = 'Network error. Please check your connection and try again.';
      }

      return {
        success: false,
        error: userFriendlyMessage,
        originalError: error.message,
        errorType: error.name,
      };
    }
  }

  /**
   * Sign out the current user
   * @returns {Promise} Sign out result
   */
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Signed out successfully',
      };
    } catch (error) {
      console.error('Sign out error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Reset password
   * @param {string} email - User email
   * @returns {Promise} Password reset result
   */
  async resetPassword(email) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'https://your-app.com/reset-password',
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Password reset email sent. Please check your inbox.',
      };
    } catch (error) {
      console.error('Password reset error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update user password
   * @param {string} newPassword - New password
   * @returns {Promise} Update password result
   */
  async updatePassword(newPassword) {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Password updated successfully',
      };
    } catch (error) {
      console.error('Update password error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get current user
   * @returns {Object|null} Current user
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Get current session
   * @returns {Object|null} Current session
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get current JWT token
   * @returns {string|null} JWT token
   */
  getAccessToken() {
    return this.currentSession?.access_token || null;
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    return !!this.currentUser && !!this.currentSession;
  }

  /**
   * Add auth state listener
   * @param {Function} listener - Listener function
   */
  addAuthListener(listener) {
    this.authListeners.push(listener);
  }

  /**
   * Remove auth state listener
   * @param {Function} listener - Listener function
   */
  removeAuthListener(listener) {
    this.authListeners = this.authListeners.filter(l => l !== listener);
  }

  /**
   * Notify all auth listeners
   * @param {string} event - Auth event
   * @param {Object} user - User object
   */
  notifyAuthListeners(event, user) {
    this.authListeners.forEach(listener => {
      try {
        listener(event, user);
      } catch (error) {
        console.error('Error in auth listener:', error);
      }
    });
  }

  /**
   * Refresh session
   * @returns {Promise} Refresh result
   */
  async refreshSession() {
    try {
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        throw error;
      }

      return {
        success: true,
        session: data.session,
      };
    } catch (error) {
      console.error('Refresh session error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

// Create and export singleton instance
const authService = new AuthService();

export default authService;
