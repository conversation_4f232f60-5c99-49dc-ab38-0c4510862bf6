/**
 * Authentication Service
 * Handles all authentication-related operations using Supabase
 */

import { supabase, AUTH_EVENTS, USER_ROLES } from '../config/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.currentSession = null;
    this.authListeners = [];
    
    // Initialize auth state
    this.initializeAuth();
  }

  /**
   * Initialize authentication state
   */
  async initializeAuth() {
    try {
      // Get current session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Error getting session:', error);
        return;
      }

      if (session) {
        this.currentSession = session;
        this.currentUser = session.user;
        this.notifyAuthListeners(AUTH_EVENTS.SIGNED_IN, session.user);
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange((event, session) => {
        this.handleAuthStateChange(event, session);
      });
    } catch (error) {
      console.error('Error initializing auth:', error);
    }
  }

  /**
   * Handle auth state changes
   */
  handleAuthStateChange(event, session) {
    console.log('Auth state changed:', event, session?.user?.email);
    
    this.currentSession = session;
    this.currentUser = session?.user || null;
    
    switch (event) {
      case 'SIGNED_IN':
        this.notifyAuthListeners(AUTH_EVENTS.SIGNED_IN, session.user);
        break;
      case 'SIGNED_OUT':
        this.notifyAuthListeners(AUTH_EVENTS.SIGNED_OUT, null);
        break;
      case 'TOKEN_REFRESHED':
        this.notifyAuthListeners(AUTH_EVENTS.TOKEN_REFRESHED, session.user);
        break;
      case 'USER_UPDATED':
        this.notifyAuthListeners(AUTH_EVENTS.USER_UPDATED, session.user);
        break;
      case 'PASSWORD_RECOVERY':
        this.notifyAuthListeners(AUTH_EVENTS.PASSWORD_RECOVERY, session.user);
        break;
    }
  }

  /**
   * Sign up a new user
   * @param {string} email - User email
   * @param {string} password - User password
   * @param {Object} metadata - Additional user metadata
   * @returns {Promise} Sign up result
   */
  async signUp(email, password, metadata = {}) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        user: data.user,
        session: data.session,
        message: 'Account created successfully. Please check your email for verification.',
      };
    } catch (error) {
      console.error('Sign up error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Sign in a user
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise} Sign in result
   */
  async signIn(email, password) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        user: data.user,
        session: data.session,
        message: 'Signed in successfully',
      };
    } catch (error) {
      console.error('Sign in error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Sign out the current user
   * @returns {Promise} Sign out result
   */
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Signed out successfully',
      };
    } catch (error) {
      console.error('Sign out error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Reset password
   * @param {string} email - User email
   * @returns {Promise} Password reset result
   */
  async resetPassword(email) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'https://your-app.com/reset-password',
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Password reset email sent. Please check your inbox.',
      };
    } catch (error) {
      console.error('Password reset error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update user password
   * @param {string} newPassword - New password
   * @returns {Promise} Update password result
   */
  async updatePassword(newPassword) {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'Password updated successfully',
      };
    } catch (error) {
      console.error('Update password error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get current user
   * @returns {Object|null} Current user
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Get current session
   * @returns {Object|null} Current session
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Get current JWT token
   * @returns {string|null} JWT token
   */
  getAccessToken() {
    return this.currentSession?.access_token || null;
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    return !!this.currentUser && !!this.currentSession;
  }

  /**
   * Add auth state listener
   * @param {Function} listener - Listener function
   */
  addAuthListener(listener) {
    this.authListeners.push(listener);
  }

  /**
   * Remove auth state listener
   * @param {Function} listener - Listener function
   */
  removeAuthListener(listener) {
    this.authListeners = this.authListeners.filter(l => l !== listener);
  }

  /**
   * Notify all auth listeners
   * @param {string} event - Auth event
   * @param {Object} user - User object
   */
  notifyAuthListeners(event, user) {
    this.authListeners.forEach(listener => {
      try {
        listener(event, user);
      } catch (error) {
        console.error('Error in auth listener:', error);
      }
    });
  }

  /**
   * Refresh session
   * @returns {Promise} Refresh result
   */
  async refreshSession() {
    try {
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        throw error;
      }

      return {
        success: true,
        session: data.session,
      };
    } catch (error) {
      console.error('Refresh session error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

// Create and export singleton instance
const authService = new AuthService();

export default authService;
