/**
 * Error Handling Utilities
 * Provides consistent error handling and user-friendly error messages
 */

/**
 * Handle API errors and provide user-friendly messages
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @returns {Object} Formatted error information
 */
export const handleApiError = (error, context = 'API') => {
  console.error(`[${context}] Error:`, error);

  let userMessage = 'An unexpected error occurred. Please try again.';
  let shouldRetry = false;
  let isNetworkError = false;

  // Check for network-related errors
  if (error.message?.includes('Network request failed') || 
      error.message?.includes('timeout') ||
      error.name === 'NetworkError' ||
      error.code === 'NETWORK_ERROR') {
    userMessage = 'Network connection issue. Please check your internet connection and try again.';
    shouldRetry = true;
    isNetworkError = true;
  }
  
  // Check for authentication errors
  else if (error.status === 401 || error.message?.includes('Unauthorized')) {
    userMessage = 'Authentication expired. Please log in again.';
    shouldRetry = false;
  }
  
  // Check for server errors
  else if (error.status >= 500) {
    userMessage = 'Server is temporarily unavailable. Please try again later.';
    shouldRetry = true;
  }
  
  // Check for client errors
  else if (error.status >= 400 && error.status < 500) {
    userMessage = error.message || 'Invalid request. Please check your input and try again.';
    shouldRetry = false;
  }
  
  // Check for timeout errors
  else if (error.message?.includes('timeout')) {
    userMessage = 'Request timed out. Please try again.';
    shouldRetry = true;
    isNetworkError = true;
  }

  return {
    userMessage,
    shouldRetry,
    isNetworkError,
    originalError: error.message,
    status: error.status,
    context
  };
};

/**
 * Handle dashboard data loading errors
 * @param {Error} error - The error object
 * @returns {Object} Dashboard error state
 */
export const handleDashboardError = (error) => {
  const errorInfo = handleApiError(error, 'DASHBOARD');
  
  return {
    ...errorInfo,
    fallbackData: {
      stats: {
        totalDeals: 0,
        activeDeals: 0,
        completedDeals: 0,
        totalProperties: 0
      },
      activities: [
        {
          id: 'error-fallback',
          type: 'system',
          title: 'Unable to load recent activity',
          description: 'Please refresh to try again',
          timestamp: new Date().toISOString(),
          color: 'gray'
        }
      ],
      profile: null
    }
  };
};

/**
 * Handle authentication errors
 * @param {Error} error - The error object
 * @returns {Object} Authentication error information
 */
export const handleAuthError = (error) => {
  const errorInfo = handleApiError(error, 'AUTH');
  
  // Specific handling for authentication errors
  if (error.message?.includes('Invalid login credentials')) {
    errorInfo.userMessage = 'Invalid email or password. Please check your credentials and try again.';
    errorInfo.shouldRetry = false;
  } else if (error.message?.includes('Email not confirmed')) {
    errorInfo.userMessage = 'Please check your email and click the verification link before signing in.';
    errorInfo.shouldRetry = false;
  } else if (error.message?.includes('Too many requests')) {
    errorInfo.userMessage = 'Too many login attempts. Please wait a moment and try again.';
    errorInfo.shouldRetry = true;
  }
  
  return errorInfo;
};

/**
 * Create a retry function for failed operations
 * @param {Function} operation - The operation to retry
 * @param {number} maxAttempts - Maximum number of retry attempts
 * @param {number} delay - Delay between retries in milliseconds
 * @returns {Function} Retry function
 */
export const createRetryFunction = (operation, maxAttempts = 3, delay = 1000) => {
  return async (...args) => {
    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation(...args);
      } catch (error) {
        lastError = error;
        
        const errorInfo = handleApiError(error);
        
        // Don't retry if it's not a retryable error
        if (!errorInfo.shouldRetry || attempt === maxAttempts) {
          throw error;
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    
    throw lastError;
  };
};

/**
 * Format error for user display
 * @param {Object} errorInfo - Error information from handleApiError
 * @returns {string} Formatted error message
 */
export const formatErrorForUser = (errorInfo) => {
  let message = errorInfo.userMessage;
  
  if (errorInfo.shouldRetry) {
    message += '\n\nYou can try again or refresh the page.';
  }
  
  if (errorInfo.isNetworkError) {
    message += '\n\nTroubleshooting:\n• Check your internet connection\n• Try switching between WiFi and mobile data\n• Contact support if the issue persists';
  }
  
  return message;
};

/**
 * Log error for debugging
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @param {Object} additionalInfo - Additional information to log
 */
export const logError = (error, context, additionalInfo = {}) => {
  const errorData = {
    context,
    message: error.message,
    stack: error.stack,
    name: error.name,
    status: error.status,
    timestamp: new Date().toISOString(),
    ...additionalInfo
  };
  
  console.error(`[ERROR-${context}]`, errorData);
  
  // In a production app, you might want to send this to an error tracking service
  // like Sentry, Bugsnag, or Crashlytics
};

/**
 * Check if error is retryable
 * @param {Error} error - The error object
 * @returns {boolean} Whether the error is retryable
 */
export const isRetryableError = (error) => {
  const errorInfo = handleApiError(error);
  return errorInfo.shouldRetry;
};

/**
 * Get error severity level
 * @param {Error} error - The error object
 * @returns {string} Severity level (low, medium, high, critical)
 */
export const getErrorSeverity = (error) => {
  if (error.status === 401) return 'high'; // Authentication issues
  if (error.status >= 500) return 'critical'; // Server errors
  if (error.message?.includes('Network')) return 'medium'; // Network issues
  if (error.status >= 400 && error.status < 500) return 'low'; // Client errors
  
  return 'medium'; // Default
};

export default {
  handleApiError,
  handleDashboardError,
  handleAuthError,
  createRetryFunction,
  formatErrorForUser,
  logError,
  isRetryableError,
  getErrorSeverity
};
