/**
 * Network Testing Utilities
 * Provides tools to test network connectivity and diagnose authentication issues
 */

import { SUPABASE_CONFIG } from '../config/supabase';

/**
 * Test basic network connectivity
 * @returns {Promise<Object>} Network test results
 */
export const testNetworkConnectivity = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    summary: {
      allPassed: false,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0
    }
  };

  // Test 1: Basic internet connectivity
  try {
    console.log('[NETWORK-TEST] Testing basic internet connectivity...');
    const startTime = Date.now();
    
    const response = await fetch('https://www.google.com', {
      method: 'HEAD',
      timeout: 10000
    });
    
    const duration = Date.now() - startTime;
    
    results.tests.internetConnectivity = {
      passed: response.ok,
      duration: duration,
      status: response.status,
      details: 'Basic internet connectivity test'
    };
  } catch (error) {
    results.tests.internetConnectivity = {
      passed: false,
      error: error.message,
      details: 'Failed to connect to internet'
    };
  }

  // Test 2: Supabase endpoint reachability
  try {
    console.log('[NETWORK-TEST] Testing Supabase endpoint reachability...');
    const startTime = Date.now();
    
    const supabaseUrl = SUPABASE_CONFIG.URL;
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'HEAD',
      timeout: 15000,
      headers: {
        'apikey': SUPABASE_CONFIG.ANON_KEY
      }
    });
    
    const duration = Date.now() - startTime;
    
    results.tests.supabaseReachability = {
      passed: response.status < 500, // Accept 4xx but not 5xx errors
      duration: duration,
      status: response.status,
      url: supabaseUrl,
      details: 'Supabase REST API endpoint reachability'
    };
  } catch (error) {
    results.tests.supabaseReachability = {
      passed: false,
      error: error.message,
      url: SUPABASE_CONFIG.URL,
      details: 'Failed to reach Supabase endpoint'
    };
  }

  // Test 3: Supabase Auth endpoint
  try {
    console.log('[NETWORK-TEST] Testing Supabase Auth endpoint...');
    const startTime = Date.now();
    
    const authUrl = `${SUPABASE_CONFIG.URL}/auth/v1/settings`;
    const response = await fetch(authUrl, {
      method: 'GET',
      timeout: 15000,
      headers: {
        'apikey': SUPABASE_CONFIG.ANON_KEY
      }
    });
    
    const duration = Date.now() - startTime;
    
    results.tests.supabaseAuth = {
      passed: response.ok,
      duration: duration,
      status: response.status,
      url: authUrl,
      details: 'Supabase Auth endpoint test'
    };
  } catch (error) {
    results.tests.supabaseAuth = {
      passed: false,
      error: error.message,
      url: `${SUPABASE_CONFIG.URL}/auth/v1/settings`,
      details: 'Failed to reach Supabase Auth endpoint'
    };
  }

  // Test 4: DNS resolution
  try {
    console.log('[NETWORK-TEST] Testing DNS resolution...');
    const startTime = Date.now();
    
    const hostname = new URL(SUPABASE_CONFIG.URL).hostname;
    // Simple DNS test by trying to connect
    const response = await fetch(`https://${hostname}`, {
      method: 'HEAD',
      timeout: 10000
    });
    
    const duration = Date.now() - startTime;
    
    results.tests.dnsResolution = {
      passed: true, // If we got here, DNS worked
      duration: duration,
      hostname: hostname,
      details: 'DNS resolution test'
    };
  } catch (error) {
    results.tests.dnsResolution = {
      passed: false,
      error: error.message,
      hostname: new URL(SUPABASE_CONFIG.URL).hostname,
      details: 'DNS resolution failed'
    };
  }

  // Calculate summary
  const testKeys = Object.keys(results.tests);
  results.summary.totalTests = testKeys.length;
  results.summary.passedTests = testKeys.filter(key => results.tests[key].passed).length;
  results.summary.failedTests = results.summary.totalTests - results.summary.passedTests;
  results.summary.allPassed = results.summary.failedTests === 0;

  console.log('[NETWORK-TEST] Test results:', results);
  return results;
};

/**
 * Test Supabase configuration
 * @returns {Object} Configuration validation results
 */
export const testSupabaseConfiguration = () => {
  const results = {
    timestamp: new Date().toISOString(),
    configuration: {
      url: SUPABASE_CONFIG.URL,
      hasAnonKey: !!SUPABASE_CONFIG.ANON_KEY,
      anonKeyLength: SUPABASE_CONFIG.ANON_KEY?.length,
      timeout: SUPABASE_CONFIG.AUTH.timeout,
      retryAttempts: SUPABASE_CONFIG.AUTH.retryAttempts
    },
    validation: {
      issues: [],
      warnings: [],
      isValid: true
    }
  };

  // Validate URL
  if (!SUPABASE_CONFIG.URL || SUPABASE_CONFIG.URL.includes('your-project-id')) {
    results.validation.issues.push('SUPABASE_URL is not configured properly');
    results.validation.isValid = false;
  }

  if (!SUPABASE_CONFIG.URL.startsWith('https://')) {
    results.validation.issues.push('SUPABASE_URL should use HTTPS');
    results.validation.isValid = false;
  }

  // Validate anon key
  if (!SUPABASE_CONFIG.ANON_KEY || SUPABASE_CONFIG.ANON_KEY.includes('your-anon-key')) {
    results.validation.issues.push('SUPABASE_ANON_KEY is not configured properly');
    results.validation.isValid = false;
  }

  if (SUPABASE_CONFIG.ANON_KEY && SUPABASE_CONFIG.ANON_KEY.length < 100) {
    results.validation.warnings.push('SUPABASE_ANON_KEY appears to be too short (should be ~100+ characters)');
  }

  // Validate timeout settings
  if (SUPABASE_CONFIG.AUTH.timeout < 10000) {
    results.validation.warnings.push('SUPABASE_TIMEOUT is quite low (< 10 seconds), consider increasing for slower networks');
  }

  if (SUPABASE_CONFIG.AUTH.timeout > 60000) {
    results.validation.warnings.push('SUPABASE_TIMEOUT is very high (> 60 seconds), consider reducing for better UX');
  }

  console.log('[CONFIG-TEST] Configuration validation:', results);
  return results;
};

/**
 * Run comprehensive network and configuration tests
 * @returns {Promise<Object>} Complete test results
 */
export const runComprehensiveTests = async () => {
  console.log('[COMPREHENSIVE-TEST] Starting comprehensive network and configuration tests...');
  
  const results = {
    timestamp: new Date().toISOString(),
    configuration: testSupabaseConfiguration(),
    network: await testNetworkConnectivity(),
    recommendations: []
  };

  // Generate recommendations based on test results
  if (!results.configuration.validation.isValid) {
    results.recommendations.push('Fix Supabase configuration issues before testing authentication');
  }

  if (!results.network.tests.internetConnectivity?.passed) {
    results.recommendations.push('Check internet connection - basic connectivity test failed');
  }

  if (!results.network.tests.supabaseReachability?.passed) {
    results.recommendations.push('Supabase endpoint is not reachable - check URL and network settings');
  }

  if (!results.network.tests.supabaseAuth?.passed) {
    results.recommendations.push('Supabase Auth endpoint is not accessible - verify API key and permissions');
  }

  if (results.network.tests.supabaseReachability?.duration > 10000) {
    results.recommendations.push('Supabase endpoint response is slow - consider increasing timeout values');
  }

  if (results.recommendations.length === 0) {
    results.recommendations.push('All tests passed - network and configuration appear to be working correctly');
  }

  console.log('[COMPREHENSIVE-TEST] Complete test results:', results);
  return results;
};

/**
 * Format test results for user display
 * @param {Object} testResults - Results from runComprehensiveTests
 * @returns {string} Formatted results for display
 */
export const formatTestResults = (testResults) => {
  let output = `Network & Configuration Test Results\n`;
  output += `Timestamp: ${testResults.timestamp}\n\n`;

  // Configuration status
  output += `Configuration Status: ${testResults.configuration.validation.isValid ? '✅ Valid' : '❌ Invalid'}\n`;
  if (testResults.configuration.validation.issues.length > 0) {
    output += `Issues: ${testResults.configuration.validation.issues.join(', ')}\n`;
  }
  if (testResults.configuration.validation.warnings.length > 0) {
    output += `Warnings: ${testResults.configuration.validation.warnings.join(', ')}\n`;
  }
  output += '\n';

  // Network test results
  output += `Network Tests:\n`;
  Object.entries(testResults.network.tests).forEach(([testName, result]) => {
    const status = result.passed ? '✅' : '❌';
    const duration = result.duration ? ` (${result.duration}ms)` : '';
    output += `${status} ${testName}${duration}\n`;
    if (!result.passed && result.error) {
      output += `   Error: ${result.error}\n`;
    }
  });
  output += '\n';

  // Recommendations
  output += `Recommendations:\n`;
  testResults.recommendations.forEach(rec => {
    output += `• ${rec}\n`;
  });

  return output;
};
