/**
 * Test Fallback Data
 * Quick test to verify fallback data is working
 */

import { 
  generateSampleDeals, 
  generateSampleProperties, 
  generateSampleProfile,
  shouldUseSampleData 
} from './fallbackData';

export const testFallbackData = () => {
  console.log('[TEST] Testing fallback data generation...');
  
  // Test sample deals
  const deals = generateSampleDeals();
  console.log('[TEST] Sample deals:', { count: deals.length, firstDeal: deals[0] });
  
  // Test sample properties
  const properties = generateSampleProperties('test-deal-1');
  console.log('[TEST] Sample properties:', { count: properties.length, firstProperty: properties[0] });
  
  // Test sample profile
  const profile = generateSampleProfile();
  console.log('[TEST] Sample profile:', profile);
  
  // Test shouldUseSampleData
  const testError1 = new Error('Resource not found.');
  const testError2 = new Error('API Error: 404 Not Found');
  const testError3 = new Error('Network request failed');
  
  console.log('[TEST] shouldUseSampleData tests:', {
    'Resource not found': shouldUseSampleData(testError1),
    'API Error 404': shouldUseSampleData(testError2),
    'Network failed': shouldUseSampleData(testError3)
  });
  
  return {
    deals,
    properties,
    profile,
    tests: {
      resourceNotFound: shouldUseSampleData(testError1),
      apiError404: shouldUseSampleData(testError2),
      networkFailed: shouldUseSampleData(testError3)
    }
  };
};

export default testFallbackData;
