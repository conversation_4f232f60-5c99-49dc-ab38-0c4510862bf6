/**
 * Quick Test Utility
 * Fast tests to verify Supabase connectivity and configuration
 */

import { SUPABASE_CONFIG } from '../config/supabase';

/**
 * Quick Supabase connectivity test
 * @returns {Promise<Object>} Test results
 */
export const quickSupabaseTest = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    success: false
  };

  try {
    console.log('[QUICK-TEST] Starting quick Supabase connectivity test...');
    
    // Test 1: Basic endpoint reachability (fast)
    const startTime = Date.now();
    
    const response = await fetch(`${SUPABASE_CONFIG.URL}/rest/v1/`, {
      method: 'HEAD',
      headers: {
        'apikey': SUPABASE_CONFIG.ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_CONFIG.ANON_KEY}`
      },
      // Short timeout for quick test
      signal: AbortSignal.timeout(5000)
    });
    
    const duration = Date.now() - startTime;
    
    results.tests.endpointReachable = {
      passed: response.status < 500,
      duration: duration,
      status: response.status,
      statusText: response.statusText
    };
    
    results.success = response.status < 500;
    
    console.log('[QUICK-TEST] Quick test completed:', results);
    return results;
    
  } catch (error) {
    console.error('[QUICK-TEST] Quick test failed:', error);
    
    results.tests.endpointReachable = {
      passed: false,
      error: error.message,
      isTimeout: error.name === 'TimeoutError' || error.message.includes('timeout'),
      isNetworkError: error.message.includes('Network') || error.message.includes('fetch')
    };
    
    results.success = false;
    return results;
  }
};

/**
 * Test Supabase auth endpoint specifically
 * @returns {Promise<Object>} Auth test results
 */
export const quickAuthTest = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    authEndpoint: null,
    success: false
  };

  try {
    console.log('[QUICK-AUTH-TEST] Testing Supabase auth endpoint...');
    
    const authUrl = `${SUPABASE_CONFIG.URL}/auth/v1/settings`;
    const startTime = Date.now();
    
    const response = await fetch(authUrl, {
      method: 'GET',
      headers: {
        'apikey': SUPABASE_CONFIG.ANON_KEY
      },
      // Short timeout for quick test
      signal: AbortSignal.timeout(5000)
    });
    
    const duration = Date.now() - startTime;
    
    results.authEndpoint = {
      passed: response.ok,
      duration: duration,
      status: response.status,
      url: authUrl
    };
    
    results.success = response.ok;
    
    console.log('[QUICK-AUTH-TEST] Auth test completed:', results);
    return results;
    
  } catch (error) {
    console.error('[QUICK-AUTH-TEST] Auth test failed:', error);
    
    results.authEndpoint = {
      passed: false,
      error: error.message,
      isTimeout: error.name === 'TimeoutError' || error.message.includes('timeout')
    };
    
    results.success = false;
    return results;
  }
};

/**
 * Validate current configuration
 * @returns {Object} Configuration validation results
 */
export const validateConfig = () => {
  const results = {
    timestamp: new Date().toISOString(),
    config: {
      url: SUPABASE_CONFIG.URL,
      hasAnonKey: !!SUPABASE_CONFIG.ANON_KEY,
      anonKeyLength: SUPABASE_CONFIG.ANON_KEY?.length,
      timeout: SUPABASE_CONFIG.AUTH.timeout
    },
    issues: [],
    warnings: [],
    isValid: true
  };

  // Check URL
  if (!SUPABASE_CONFIG.URL || SUPABASE_CONFIG.URL.includes('your-project-id')) {
    results.issues.push('SUPABASE_URL not configured');
    results.isValid = false;
  }

  // Check anon key
  if (!SUPABASE_CONFIG.ANON_KEY || SUPABASE_CONFIG.ANON_KEY.includes('your-anon-key')) {
    results.issues.push('SUPABASE_ANON_KEY not configured');
    results.isValid = false;
  }

  // Check timeout
  if (SUPABASE_CONFIG.AUTH.timeout < 5000) {
    results.warnings.push('Timeout is very low (< 5 seconds)');
  }

  console.log('[CONFIG-VALIDATION] Configuration validation:', results);
  return results;
};

/**
 * Run all quick tests
 * @returns {Promise<Object>} Complete test results
 */
export const runQuickTests = async () => {
  console.log('[QUICK-TESTS] Starting all quick tests...');
  
  const results = {
    timestamp: new Date().toISOString(),
    config: validateConfig(),
    connectivity: null,
    auth: null,
    overallSuccess: false,
    recommendations: []
  };

  // Only run network tests if config is valid
  if (results.config.isValid) {
    try {
      // Run connectivity test
      results.connectivity = await quickSupabaseTest();
      
      // Run auth test if connectivity passed
      if (results.connectivity.success) {
        results.auth = await quickAuthTest();
      }
    } catch (error) {
      console.error('[QUICK-TESTS] Test execution failed:', error);
    }
  }

  // Determine overall success
  results.overallSuccess = results.config.isValid && 
                          results.connectivity?.success && 
                          results.auth?.success;

  // Generate recommendations
  if (!results.config.isValid) {
    results.recommendations.push('Fix configuration issues first');
  } else if (!results.connectivity?.success) {
    results.recommendations.push('Check network connectivity to Supabase');
  } else if (!results.auth?.success) {
    results.recommendations.push('Check Supabase auth configuration');
  } else {
    results.recommendations.push('All quick tests passed - ready for authentication');
  }

  console.log('[QUICK-TESTS] All quick tests completed:', results);
  return results;
};

/**
 * Format quick test results for display
 * @param {Object} testResults - Results from runQuickTests
 * @returns {string} Formatted results
 */
export const formatQuickResults = (testResults) => {
  let output = `Quick Test Results\n`;
  output += `Time: ${new Date(testResults.timestamp).toLocaleTimeString()}\n\n`;

  // Configuration
  output += `Config: ${testResults.config.isValid ? '✅' : '❌'}\n`;
  if (testResults.config.issues.length > 0) {
    output += `Issues: ${testResults.config.issues.join(', ')}\n`;
  }

  // Connectivity
  if (testResults.connectivity) {
    const conn = testResults.connectivity.tests.endpointReachable;
    output += `Connectivity: ${conn.passed ? '✅' : '❌'}`;
    if (conn.duration) output += ` (${conn.duration}ms)`;
    output += '\n';
  }

  // Auth
  if (testResults.auth) {
    const auth = testResults.auth.authEndpoint;
    output += `Auth: ${auth.passed ? '✅' : '❌'}`;
    if (auth.duration) output += ` (${auth.duration}ms)`;
    output += '\n';
  }

  output += `\nOverall: ${testResults.overallSuccess ? '✅ Ready' : '❌ Issues Found'}\n`;
  
  if (testResults.recommendations.length > 0) {
    output += `\nNext: ${testResults.recommendations[0]}`;
  }

  return output;
};
