/**
 * Fallback Data Provider
 * Provides sample data when API endpoints are not available or return 404
 */

/**
 * Generate sample deals data
 * @returns {Array} Sample deals array
 */
export const generateSampleDeals = () => {
  const statuses = ['active', 'in_progress', 'pending', 'completed', 'closed'];
  const dealTypes = ['Purchase', 'Sale', 'Rental', 'Commercial'];
  
  return [
    {
      id: 'sample-deal-1',
      title: 'Downtown Condo Purchase',
      name: 'Downtown Condo Purchase',
      status: 'active',
      type: dealTypes[0],
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
      value: 450000,
      clientName: '<PERSON>'
    },
    {
      id: 'sample-deal-2',
      title: 'Suburban House Sale',
      name: 'Suburban House Sale',
      status: 'in_progress',
      type: dealTypes[1],
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
      updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
      value: 675000,
      clientName: '<PERSON>'
    },
    {
      id: 'sample-deal-3',
      title: 'Office Space Rental',
      name: 'Office Space Rental',
      status: 'pending',
      type: dealTypes[2],
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      value: 3500,
      clientName: 'Tech Startup Inc'
    },
    {
      id: 'sample-deal-4',
      title: 'Luxury Villa Purchase',
      name: 'Luxury Villa Purchase',
      status: 'completed',
      type: dealTypes[0],
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      value: 1200000,
      clientName: 'Michael Brown'
    },
    {
      id: 'sample-deal-5',
      title: 'Commercial Building Sale',
      name: 'Commercial Building Sale',
      status: 'closed',
      type: dealTypes[3],
      createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(), // 21 days ago
      updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      value: 2500000,
      clientName: 'Investment Group LLC'
    }
  ];
};

/**
 * Generate sample properties for a deal
 * @param {string} dealId - Deal ID
 * @returns {Array} Sample properties array
 */
export const generateSampleProperties = (dealId) => {
  const propertyTypes = ['Condo', 'House', 'Apartment', 'Office', 'Retail'];
  const addresses = [
    '123 Main St, Downtown',
    '456 Oak Ave, Suburbs',
    '789 Pine Rd, Uptown',
    '321 Elm St, Midtown',
    '654 Maple Dr, Westside'
  ];
  
  // Generate 1-3 properties per deal
  const numProperties = Math.floor(Math.random() * 3) + 1;
  const properties = [];
  
  for (let i = 0; i < numProperties; i++) {
    properties.push({
      id: `sample-property-${dealId}-${i + 1}`,
      dealId: dealId,
      title: `${propertyTypes[i % propertyTypes.length]} Property`,
      address: addresses[i % addresses.length],
      type: propertyTypes[i % propertyTypes.length],
      bedrooms: Math.floor(Math.random() * 4) + 1,
      bathrooms: Math.floor(Math.random() * 3) + 1,
      squareFeet: Math.floor(Math.random() * 2000) + 800,
      price: Math.floor(Math.random() * 500000) + 200000,
      createdAt: new Date(Date.now() - (i + 1) * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - i * 60 * 60 * 1000).toISOString()
    });
  }
  
  return properties;
};

/**
 * Generate sample user profile
 * @returns {Object} Sample profile object
 */
export const generateSampleProfile = () => {
  return {
    id: 'sample-profile-1',
    email: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    role: 'realtor',
    phone: '+****************',
    company: 'OnRoad Real Estate',
    license: 'RE123456789',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() // 1 day ago
  };
};

/**
 * Generate sample dashboard statistics
 * @returns {Object} Sample stats object
 */
export const generateSampleStats = () => {
  const deals = generateSampleDeals();
  
  const totalDeals = deals.length;
  const activeDeals = deals.filter(deal => 
    deal.status === 'active' || deal.status === 'in_progress' || deal.status === 'pending'
  ).length;
  const completedDeals = deals.filter(deal => 
    deal.status === 'completed' || deal.status === 'closed'
  ).length;
  
  // Calculate total properties
  let totalProperties = 0;
  deals.forEach(deal => {
    const properties = generateSampleProperties(deal.id);
    totalProperties += properties.length;
  });
  
  return {
    totalDeals,
    activeDeals,
    completedDeals,
    totalProperties,
    lastUpdated: new Date().toISOString(),
    isSampleData: true
  };
};

/**
 * Generate sample recent activities
 * @param {number} limit - Number of activities to generate
 * @returns {Array} Sample activities array
 */
export const generateSampleActivities = (limit = 10) => {
  const deals = generateSampleDeals();
  const activities = [];
  
  // Generate activities from deals
  deals.forEach((deal, index) => {
    // Deal update activity
    activities.push({
      id: `sample-activity-deal-${deal.id}`,
      type: 'deal_update',
      title: `${deal.title} updated`,
      description: `Deal status changed to ${deal.status}`,
      timestamp: deal.updatedAt,
      color: deal.status === 'completed' || deal.status === 'closed' ? 'green' : 'blue'
    });
    
    // Property activity
    if (index < 3) { // Only for first 3 deals
      const properties = generateSampleProperties(deal.id);
      if (properties.length > 0) {
        activities.push({
          id: `sample-activity-property-${properties[0].id}`,
          type: 'property_added',
          title: 'New property added',
          description: `${properties[0].title} at ${properties[0].address}`,
          timestamp: properties[0].createdAt,
          color: 'purple'
        });
      }
    }
  });
  
  // Add some system activities
  activities.push({
    id: 'sample-activity-welcome',
    type: 'system',
    title: 'Welcome to OnRoad',
    description: 'Your account has been set up successfully',
    timestamp: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    color: 'blue'
  });
  
  // Sort by timestamp and limit
  return activities
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    .slice(0, limit);
};

/**
 * Get comprehensive sample dashboard data
 * @returns {Object} Complete sample dashboard data
 */
export const getSampleDashboardData = () => {
  return {
    stats: generateSampleStats(),
    activities: generateSampleActivities(),
    profile: generateSampleProfile(),
    deals: generateSampleDeals(),
    lastUpdated: new Date().toISOString(),
    isSampleData: true
  };
};

/**
 * Check if we should use sample data based on error
 * @param {Error} error - The error object
 * @returns {boolean} Whether to use sample data
 */
export const shouldUseSampleData = (error) => {
  console.log('[FALLBACK] Checking if should use sample data:', {
    message: error.message,
    status: error.status,
    name: error.name
  });

  const shouldUse = error.message?.includes('Resource not found') ||
                    error.message?.includes('Not Found') ||
                    error.status === 404 ||
                    error.message?.includes('Network request failed') ||
                    error.message?.includes('API Error: 404');

  console.log('[FALLBACK] Should use sample data:', shouldUse);
  return shouldUse;
};

export default {
  generateSampleDeals,
  generateSampleProperties,
  generateSampleProfile,
  generateSampleStats,
  generateSampleActivities,
  getSampleDashboardData,
  shouldUseSampleData
};
