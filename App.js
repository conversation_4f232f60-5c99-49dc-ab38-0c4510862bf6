import React from 'react';
import { SafeAreaView, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import LoginScreen from './src/screens/LoginScreen';
import './global.css';

// Mock navigation for now
const mockNavigation = {
  navigate: (screen) => {
    console.log(`Navigate to: ${screen}`);
  },
};

export default function App() {
  console.log('App component rendering...');

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" backgroundColor="#ffffff" />
      <LoginScreen navigation={mockNavigation} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
});
