# Development Environment Configuration
# This file contains environment variables for development

# API Configuration
API_BASE_URL=https://onroad-express-3d680c74f3cc.herokuapp.com/api
API_TIMEOUT=30000

# Supabase Configuration (Replace with your actual values)
SUPABASE_URL=https://hxdzdvocsoqzevdkrjze.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh4ZHpkdm9jc29xemV2ZGtyanplIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4MTc5NzYsImV4cCI6MjA1NzM5Mzk3Nn0.Gd-o8xz3vIkOWcmCVvK0KEBM0GUdJXBJtnsBEWb1eKQ
SUPABASE_JWT_SECRET=Fc/u0wX08kkSg08SF2IbM5Ihh2HKo49sXlFkPbPle8YVi1rOa43TXLXRA/x1zPJCGrF9jlZUnomS13eMGVzKYg==

# App Configuration
APP_ENV=development
APP_VERSION=1.0.0

# Debug Configuration
DEBUG_MODE=true
ENABLE_FLIPPER=true
ENABLE_HOT_RELOAD=true

# Metro Configuration
METRO_PORT=8081
METRO_HOST=localhost

# Device Configuration
ENABLE_DEVICE_LOGS=true
LOG_LEVEL=debug

# Performance Configuration
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_MEMORY_PROFILING=false

# Feature Flags
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_FEATURES=true
