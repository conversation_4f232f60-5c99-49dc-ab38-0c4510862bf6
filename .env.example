# OnRoad App Environment Configuration
# Copy this file to .env.development, .env.staging, and .env.production
# and fill in the appropriate values for each environment

# API Configuration
API_BASE_URL=https://your-express-api.example.com
API_TIMEOUT=30000
DEBUG_MODE=true

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here

# App Configuration
APP_ENV=development
APP_VERSION=1.0.0

# Security
JWT_SECRET=your-jwt-secret-here

# Optional: Analytics and Monitoring
SENTRY_DSN=your-sentry-dsn-here
ANALYTICS_KEY=your-analytics-key-here
