const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add iOS simulator specific configurations
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Configure transformer for better iOS simulator compatibility
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    // Disable minification in development for better debugging
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

// Configure serializer for iOS simulator
config.serializer = {
  ...config.serializer,
  customSerializer: null,
};

// Add watchFolders to prevent file watching issues
config.watchFolders = [__dirname];

// Configure server for better stability
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Add CORS headers for iOS simulator
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      
      return middleware(req, res, next);
    };
  },
};

module.exports = withNativeWind(config, { input: './global.css' });
