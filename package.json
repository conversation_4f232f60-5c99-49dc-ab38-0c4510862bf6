{"name": "onRoad-App", "version": "1.0.0", "description": "React Native application for onRoad", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@expo/cli": "^0.21.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "react-dom": "^19.0.0", "react-native-web": "^0.20.0", "react-test-renderer": "19.0.0"}, "dependencies": {"@expo/metro-config": "^0.20.14", "@react-native-async-storage/async-storage": "^2.1.2", "@supabase/supabase-js": "^2.49.9", "expo": "~53.0.0", "expo-constants": "^17.1.6", "expo-status-bar": "~2.2.3", "formik": "^2.4.6", "metro": "^0.82.4", "metro-cache": "^0.82.4", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.2", "tailwindcss": "^3.3.0", "yup": "^1.6.1"}}