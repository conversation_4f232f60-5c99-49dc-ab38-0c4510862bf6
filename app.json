{"expo": {"name": "onRoad App", "slug": "onroad-app", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "splash": {"resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.onroadapp.mobile", "simulator": true, "jsEngine": "hermes"}, "android": {"package": "com.onroadapp.mobile"}, "scheme": "onroad-app", "extra": {"eas": {"projectId": "your-project-id-here"}, "supabaseUrl": "https://hxdzdvocsoqzevdkrjze.supabase.co", "supabaseAnonKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh4ZHpkdm9jc29xemV2ZGtyanplIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4MTc5NzYsImV4cCI6MjA1NzM5Mzk3Nn0.Gd-o8xz3vIkOWcmCVvK0KEBM0GUdJXBJtnsBEWb1eKQ", "supabaseJwtSecret": "Fc/u0wX08kkSg08SF2IbM5Ihh2HKo49sXlFkPbPle8YVi1rOa43TXLXRA/x1zPJCGrF9jlZUnomS13eMGVzKYg==", "apiBaseUrl": "https://onroad-express-3d680c74f3cc.herokuapp.com/api", "apiTimeout": 30000, "supabaseTimeout": 60000, "supabaseRetryAttempts": 5, "supabaseRetryDelay": 2000, "useCustomFetch": false, "debugMode": true}}}