#!/bin/bash

# iOS Simulator Diagnostics Script
# This script helps identify the exact point of failure in iOS simulator

echo "🔍 iOS Simulator Diagnostics Starting..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Test 1: Check system requirements
print_test "1. Checking system requirements..."

if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_pass "Node.js: $NODE_VERSION"
else
    print_fail "Node.js not found"
fi

if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_pass "npm: $NPM_VERSION"
else
    print_fail "npm not found"
fi

if command -v xcrun &> /dev/null; then
    print_pass "Xcode Command Line Tools: Available"
else
    print_fail "Xcode Command Line Tools not found"
fi

# Test 2: Check Expo installation
print_test "2. Checking Expo installation..."

if [ -f "node_modules/.bin/expo" ]; then
    EXPO_VERSION=$(npx expo --version)
    print_pass "Expo CLI: $EXPO_VERSION"
else
    print_fail "Expo CLI not found in node_modules"
fi

# Test 3: Check React Native compatibility
print_test "3. Checking React Native compatibility..."

RN_VERSION=$(node -p "require('./package.json').dependencies['react-native']")
EXPO_VERSION=$(node -p "require('./package.json').dependencies.expo")
print_info "React Native: $RN_VERSION"
print_info "Expo SDK: $EXPO_VERSION"

# Test 4: Check iOS Simulator availability
print_test "4. Checking iOS Simulator availability..."

if command -v xcrun &> /dev/null; then
    SIMULATORS=$(xcrun simctl list devices available | grep "iPhone" | head -3)
    if [ -n "$SIMULATORS" ]; then
        print_pass "iOS Simulators available:"
        echo "$SIMULATORS"
    else
        print_fail "No iOS Simulators available"
    fi
else
    print_fail "Cannot check simulators - Xcode not available"
fi

# Test 5: Test Metro bundler
print_test "5. Testing Metro bundler (30 seconds)..."

timeout 30s npx expo start --clear --no-dev --minify false > metro_test.log 2>&1 &
METRO_PID=$!

sleep 25
if kill -0 $METRO_PID 2>/dev/null; then
    print_pass "Metro bundler started successfully"
    kill $METRO_PID 2>/dev/null
else
    print_fail "Metro bundler failed to start"
fi

# Check metro log for errors
if [ -f "metro_test.log" ]; then
    if grep -q "error\|Error\|ERROR" metro_test.log; then
        print_fail "Metro bundler errors found:"
        grep -i "error" metro_test.log | head -5
    else
        print_pass "No Metro bundler errors detected"
    fi
fi

# Test 6: Check for conflicting processes
print_test "6. Checking for conflicting processes..."

CONFLICTING_PROCESSES=$(ps aux | grep -E "(expo|metro|react-native)" | grep -v grep | wc -l)
if [ "$CONFLICTING_PROCESSES" -gt 0 ]; then
    print_fail "Found $CONFLICTING_PROCESSES conflicting processes"
    ps aux | grep -E "(expo|metro|react-native)" | grep -v grep
else
    print_pass "No conflicting processes found"
fi

# Test 7: Check file permissions
print_test "7. Checking file permissions..."

if [ -r "package.json" ] && [ -r "App.js" ] && [ -r "app.json" ]; then
    print_pass "Core files are readable"
else
    print_fail "Some core files are not readable"
fi

# Test 8: Check for problematic dependencies
print_test "8. Checking for problematic dependencies..."

PROBLEMATIC_DEPS=$(npm ls --depth=0 2>&1 | grep -E "(UNMET|missing|invalid)" | wc -l)
if [ "$PROBLEMATIC_DEPS" -eq 0 ]; then
    print_pass "No problematic dependencies found"
else
    print_fail "Found $PROBLEMATIC_DEPS problematic dependencies"
    npm ls --depth=0 2>&1 | grep -E "(UNMET|missing|invalid)"
fi

# Test 9: Generate detailed crash report
print_test "9. Generating detailed crash report..."

cat > crash_report.md << EOF
# iOS Simulator Crash Report

## System Information
- Node.js: $(node --version 2>/dev/null || echo "Not found")
- npm: $(npm --version 2>/dev/null || echo "Not found")
- Expo CLI: $(npx expo --version 2>/dev/null || echo "Not found")
- macOS: $(sw_vers -productVersion 2>/dev/null || echo "Unknown")
- Xcode: $(xcodebuild -version 2>/dev/null | head -1 || echo "Not found")

## Project Configuration
- React Native: $(node -p "require('./package.json').dependencies['react-native']" 2>/dev/null || echo "Unknown")
- Expo SDK: $(node -p "require('./package.json').dependencies.expo" 2>/dev/null || echo "Unknown")
- React: $(node -p "require('./package.json').dependencies.react" 2>/dev/null || echo "Unknown")

## Available iOS Simulators
$(xcrun simctl list devices available 2>/dev/null | grep "iPhone" || echo "Cannot list simulators")

## Metro Bundler Log (Last 20 lines)
$(tail -20 metro_test.log 2>/dev/null || echo "No metro log available")

## Dependency Issues
$(npm ls --depth=0 2>&1 | grep -E "(UNMET|missing|invalid)" || echo "No dependency issues found")

## Recommended Next Steps
1. Run complete reset: ./complete-reset-procedure.sh
2. Try alternative React Native version
3. Test with minimal app configuration
4. Check iOS Simulator logs in Console.app
EOF

print_pass "Crash report generated: crash_report.md"

# Cleanup
rm -f metro_test.log

echo ""
print_info "🎯 Diagnostics Complete!"
echo ""
echo "📋 Summary:"
echo "- Check the generated crash_report.md for detailed information"
echo "- If Metro bundler failed, there may be configuration issues"
echo "- If simulators are not available, reinstall Xcode"
echo "- If dependencies have issues, run the complete reset procedure"
echo ""
echo "🔧 Next Actions:"
echo "1. Review crash_report.md"
echo "2. Run: ./complete-reset-procedure.sh"
echo "3. Try alternative solutions below"
