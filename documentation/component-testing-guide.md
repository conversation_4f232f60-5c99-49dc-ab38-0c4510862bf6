# 🧪 Individual Component Testing Guide

## 🎯 **Root Cause Identified: Node.js v24.1.0 Incompatibility**

Your iOS simulator crash is caused by **Node.js v24.1.0** being incompatible with:
- React Native 0.74.5
- Expo SDK 53
- Metro bundler

## 🔧 **IMMEDIATE FIX REQUIRED**

### **Step 1: Fix Node.js Version (CRITICAL)**

**Option A: Using nvm (Recommended)**
```bash
# Install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Restart terminal or reload
source ~/.bashrc

# Install Node.js 20 LTS
nvm install 20
nvm use 20
nvm alias default 20

# Verify
node --version  # Should show v20.x.x
```

**Option B: Using Homebrew**
```bash
# Uninstall current Node.js
brew uninstall node

# Install Node.js 20
brew install node@20
brew link node@20 --force

# Verify
node --version  # Should show v20.x.x
```

### **Step 2: Complete Environment Reset**
```bash
# After fixing Node.js version
./complete-reset-procedure.sh
```

## 🧪 **Individual Component Testing**

### **Test 1: Node.js Compatibility**
```bash
# Check Node.js version
node --version
# ✅ Should be v18.x.x or v20.x.x
# ❌ v21.x.x, v22.x.x, v24.x.x are incompatible

# Test npm functionality
npm --version
# ✅ Should work without errors
```

### **Test 2: Metro Bundler**
```bash
# Test Metro bundler startup
npx expo start --clear --no-dev --minify false

# Expected output:
# ✅ "Metro waiting on exp://..."
# ✅ QR code displayed
# ❌ "non-std C++ exception" = Node.js issue
```

### **Test 3: iOS Simulator Launch**
```bash
# Start Expo
npx expo start

# Press 'i' to launch iOS simulator
# Expected behavior:
# ✅ Simulator opens and app loads
# ❌ Immediate crash = still Node.js issue
```

### **Test 4: Physical Device (Control Test)**
```bash
# Start Expo
npx expo start

# Scan QR code with Expo Go
# Expected behavior:
# ✅ Should work (confirms app code is fine)
# ❌ If this fails too = broader issue
```

### **Test 5: Minimal App Test**
Create a minimal test app to isolate issues:

```javascript
// Create minimal-test.js
import React from 'react';
import { View, Text } from 'react-native';

export default function MinimalTest() {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text>Minimal Test App</Text>
    </View>
  );
}
```

```javascript
// Temporarily replace App.js content
import MinimalTest from './minimal-test';
export default MinimalTest;
```

## 🔍 **Step 5: Debugging Techniques**

### **iOS Simulator Console Logs**

1. **Open Console.app** (macOS application)
2. **Filter by device**: Select your iOS Simulator
3. **Filter by process**: Search for "Expo Go" or "ExpoKit"
4. **Look for crash logs** with timestamps matching your crash

### **Metro Bundler Verbose Logging**
```bash
# Enable verbose logging
npx expo start --clear --verbose

# Look for specific errors:
# - "Cannot resolve module"
# - "Transform error"
# - "Native module missing"
```

### **React Native Debugger**
```bash
# Install React Native Debugger
brew install --cask react-native-debugger

# Start with debugging
npx expo start --dev-client

# Enable remote debugging in Expo Go
# Shake device → "Debug Remote JS"
```

### **Xcode Simulator Logs**
```bash
# View simulator logs
xcrun simctl spawn booted log stream --predicate 'process == "Expo Go"'

# Or check crash reports
ls ~/Library/Logs/DiagnosticReports/ | grep -i expo
```

### **Network Debugging**
```bash
# Test with different network modes
npx expo start --localhost    # Local only
npx expo start --tunnel       # Public tunnel
npx expo start --lan         # LAN mode (default)
```

## 🎯 **Expected Results After Node.js Fix**

### **Before Fix (Current State)**
- ❌ Metro bundler fails to start
- ❌ iOS simulator crashes with C++ exception
- ❌ "timeout: command not found" errors
- ✅ Physical device works (because it bypasses local Metro)

### **After Fix (Expected State)**
- ✅ Metro bundler starts successfully
- ✅ iOS simulator opens and loads app
- ✅ Hot reloading works
- ✅ No C++ exceptions
- ✅ Both physical device and simulator work

## 🚨 **If Node.js Fix Doesn't Work**

### **Alternative Solutions**

1. **Try Different React Native Version**
```bash
npm install react-native@0.73.6
```

2. **Disable Hermes Engine**
```json
// In app.json
"ios": {
  "jsEngine": "jsc"  // Instead of "hermes"
}
```

3. **Use Expo SDK 51 (More Stable)**
```bash
npm install expo@~51.0.0
npx expo install --fix
```

4. **Create New Expo Project (Last Resort)**
```bash
npx create-expo-app TestApp
# Copy your src/ folder to new project
```

## 📊 **Success Indicators**

### **✅ Fixed Successfully**
- Metro bundler starts without errors
- iOS simulator opens app without crashing
- Console shows "App component rendering..."
- Hot reloading works on simulator
- No C++ exceptions in logs

### **❌ Still Broken**
- Metro bundler fails to start
- iOS simulator crashes immediately
- C++ exception errors persist
- "timeout: command not found" errors

## 🎯 **Next Steps**

1. **Fix Node.js version** (CRITICAL - this will solve 90% of the issue)
2. **Run complete reset procedure**
3. **Test each component individually**
4. **Check iOS Simulator Console logs**
5. **If still failing, try alternative solutions**

The Node.js version incompatibility is almost certainly the root cause of your iOS simulator crash. Once you downgrade to Node.js 20 LTS, the issue should be completely resolved.
