# Environment Setup Guide

## Overview

This guide covers the complete environment setup for the OnRoad React Native application, including development tools, dependencies, and configuration.

## Prerequisites

### Required Software

#### Node.js and npm
- **Node.js**: Version 18.x or higher
- **npm**: Version 9.x or higher (comes with Node.js)
- **Verification**:
  ```bash
  node --version  # Should show v18.x.x or higher
  npm --version   # Should show 9.x.x or higher
  ```

#### Expo CLI
- **Installation**:
  ```bash
  npm install -g @expo/cli
  ```
- **Verification**:
  ```bash
  expo --version
  ```

#### Git
- **Installation**: Download from [git-scm.com](https://git-scm.com/)
- **Verification**:
  ```bash
  git --version
  ```

### Optional but Recommended

#### Yarn (Alternative to npm)
- **Installation**:
  ```bash
  npm install -g yarn
  ```
- **Verification**:
  ```bash
  yarn --version
  ```

#### VS Code
- **Download**: [Visual Studio Code](https://code.visualstudio.com/)
- **Recommended Extensions**:
  - React Native Tools
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter
  - ESLint
  - Tailwind CSS IntelliSense

## Project Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd onRoad-App
```

### 2. Install Dependencies
```bash
# Using npm
npm install

# Or using yarn
yarn install
```

### 3. Environment Configuration

#### Create Environment Files
```bash
# Copy example environment file
cp .env.example .env.development

# Create additional environment files as needed
cp .env.example .env.staging
cp .env.example .env.production
```

#### Configure Environment Variables

Edit `.env.development` with your specific values:

```bash
# API Configuration
API_BASE_URL=http://localhost:3000/api
API_TIMEOUT=30000
DEBUG_MODE=true

# Supabase Configuration (Replace with your actual values)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here

# App Configuration
APP_ENV=development
APP_VERSION=1.0.0

# Debug Configuration
ENABLE_FLIPPER=true
ENABLE_HOT_RELOAD=true

# Metro Configuration
METRO_PORT=8081
METRO_HOST=localhost

# Device Configuration
ENABLE_DEVICE_LOGS=true
LOG_LEVEL=debug

# Performance Configuration
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_MEMORY_PROFILING=false

# Feature Flags
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_FEATURES=true
```

### 4. Verify Installation

#### Start Development Server
```bash
npm start
# Or
yarn start
```

#### Expected Output
```
Starting project at /path/to/onRoad-App
Starting Metro Bundler

▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄
█ ▄▄▄▄▄ █▄▄▄ ▀▀▀█▄█ ▄▄▄▄▄ █
█ █   █ ██▄▀ █ ▀▀▀█ █   █ █
█ █▄▄▄█ ██▀▄ ▄▄██▀█ █▄▄▄█ █
█▄▄▄▄▄▄▄█ ▀▄█ ▀ █▄█▄▄▄▄▄▄▄█

› Metro waiting on exp://192.168.x.x:8081
› Scan the QR code above with Expo Go (Android) or the Camera app (iOS)
› Web is waiting on http://localhost:8081
```

## Development Tools Setup

### Mobile Development

#### iOS Development (macOS only)
1. **Install Xcode**: Download from Mac App Store
2. **Install iOS Simulator**: Included with Xcode
3. **Command Line Tools**:
   ```bash
   xcode-select --install
   ```

#### Android Development
1. **Install Android Studio**: Download from [developer.android.com](https://developer.android.com/studio)
2. **Configure Android SDK**: Follow Android Studio setup wizard
3. **Create Virtual Device**: Use AVD Manager in Android Studio
4. **Environment Variables** (add to your shell profile):
   ```bash
   export ANDROID_HOME=$HOME/Library/Android/sdk
   export PATH=$PATH:$ANDROID_HOME/emulator
   export PATH=$PATH:$ANDROID_HOME/tools
   export PATH=$PATH:$ANDROID_HOME/tools/bin
   export PATH=$PATH:$ANDROID_HOME/platform-tools
   ```

### Testing Setup

#### Install Testing Dependencies
```bash
# Jest and React Native Testing Library (already included)
npm install --save-dev jest @testing-library/react-native

# Additional testing utilities
npm install --save-dev jest-environment-jsdom @babel/preset-env @babel/preset-react
```

#### Run Tests
```bash
npm test
# Or
yarn test
```

### Code Quality Tools

#### ESLint Configuration
ESLint is already configured. To run manually:
```bash
npx eslint src/
```

#### Prettier Configuration
Prettier is configured for code formatting:
```bash
npx prettier --write src/
```

## Platform-Specific Setup

### Web Development
- **Browser**: Chrome, Firefox, Safari, or Edge
- **Development Tools**: Browser developer tools
- **Testing**: Responsive design mode for mobile testing

### Expo Go Setup
1. **Install Expo Go**:
   - iOS: Download from App Store
   - Android: Download from Google Play Store
2. **Account**: Create Expo account (optional but recommended)
3. **Testing**: Scan QR code from development server

## Configuration Files

### Key Configuration Files

#### `babel.config.js`
- Babel configuration for React Native and NativeWind
- Test environment configuration
- Plugin configuration

#### `metro.config.js`
- Metro bundler configuration
- Platform-specific settings
- Asset resolution

#### `tailwind.config.js`
- Tailwind CSS configuration
- Content paths for purging
- Theme customization

#### `package.json`
- Dependencies and devDependencies
- Scripts for development and testing
- Project metadata

## Environment Variables Reference

### Required Variables
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `API_BASE_URL`: Backend API base URL

### Optional Variables
- `DEBUG_MODE`: Enable debug logging (true/false)
- `API_TIMEOUT`: API request timeout in milliseconds
- `APP_ENV`: Environment name (development/staging/production)

## Troubleshooting

### Common Issues

#### Metro Bundler Issues
```bash
# Clear Metro cache
npx expo start --clear

# Reset Metro cache completely
npx react-native start --reset-cache
```

#### Node Modules Issues
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

#### iOS Simulator Issues
```bash
# Reset iOS Simulator
xcrun simctl erase all
```

#### Android Emulator Issues
```bash
# Cold boot Android emulator
emulator -avd <device_name> -cold-boot
```

### Environment Variable Issues
1. **Check file exists**: Ensure `.env.development` exists
2. **Restart server**: Restart development server after changes
3. **Check syntax**: Ensure no spaces around `=` in env files
4. **Verify loading**: Check console for environment variable loading

### Port Conflicts
```bash
# Kill process on port 8081
lsof -ti:8081 | xargs kill -9

# Use different port
npx expo start --port 8082
```

## Next Steps

After completing the environment setup:

1. **Review Documentation**: Read through implementation guides
2. **Configure Supabase**: Set up your Supabase project
3. **Test Authentication**: Verify authentication flow works
4. **Explore Features**: Navigate through the app screens
5. **Start Development**: Begin implementing new features

## Additional Resources

- **Expo Documentation**: [docs.expo.dev](https://docs.expo.dev/)
- **React Native Documentation**: [reactnative.dev](https://reactnative.dev/)
- **Tailwind CSS**: [tailwindcss.com](https://tailwindcss.com/)
- **Supabase Documentation**: [supabase.com/docs](https://supabase.com/docs)
- **NativeWind Documentation**: [nativewind.dev](https://www.nativewind.dev/)
