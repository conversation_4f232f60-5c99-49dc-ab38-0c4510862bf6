# Common Issues and Solutions

## Overview

This document provides solutions to common issues encountered during OnRoad app development and deployment.

## Authentication Issues

### Supabase Configuration Errors

#### Issue: "Invalid API URL" or "Invalid API Key"
**Symptoms**:
- Authentication fails silently
- <PERSON>sol<PERSON> shows Supabase connection errors
- Login attempts return generic errors

**Solutions**:
1. **Verify Environment Variables**:
   ```bash
   # Check .env.development file
   cat .env.development | grep SUPABASE
   ```

2. **Validate Supabase Credentials**:
   - Log into Supabase dashboard
   - Go to Settings → API
   - Copy the correct Project URL and anon/public key
   - Update `.env.development`:
     ```bash
     SUPABASE_URL=https://your-actual-project-id.supabase.co
     SUPABASE_ANON_KEY=your-actual-anon-key
     ```

3. **Restart Development Server**:
   ```bash
   # Stop current server (Ctrl+C)
   npm start --clear
   ```

#### Issue: "Auth session not found"
**Symptoms**:
- User appears logged in but API calls fail
- Token refresh errors
- Inconsistent authentication state

**Solutions**:
1. **Clear AsyncStorage**:
   ```javascript
   // In development, clear storage
   import AsyncStorage from '@react-native-async-storage/async-storage';
   AsyncStorage.clear();
   ```

2. **Check Token Expiration**:
   ```javascript
   // Debug token in auth service
   console.log('Current session:', authService.getCurrentSession());
   console.log('Token expires at:', session?.expires_at);
   ```

3. **Force Re-authentication**:
   ```javascript
   // Sign out and sign in again
   await authService.signOut();
   // Navigate to login screen
   ```

### API Authentication Errors

#### Issue: "401 Unauthorized" on API calls
**Symptoms**:
- Authentication works but API calls fail
- "Bearer token invalid" errors
- firstSignIn endpoint returns 401

**Solutions**:
1. **Check Token Format**:
   ```javascript
   // Verify token is being sent correctly
   const token = authService.getAccessToken();
   console.log('Token:', token?.substring(0, 20) + '...');
   ```

2. **Verify API Configuration**:
   ```javascript
   // Check API base URL
   console.log('API Base URL:', API_CONFIG.BASE_URL);
   ```

3. **Test Token Manually**:
   ```bash
   # Test API endpoint with curl
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        -X POST \
        http://localhost:3000/api/firstSignin
   ```

## Build and Development Issues

### Metro Bundler Problems

#### Issue: "Metro bundler failed to start"
**Symptoms**:
- Development server won't start
- Port already in use errors
- Bundle loading failures

**Solutions**:
1. **Clear Metro Cache**:
   ```bash
   npx expo start --clear
   # Or
   npx react-native start --reset-cache
   ```

2. **Kill Existing Processes**:
   ```bash
   # Kill processes on port 8081
   lsof -ti:8081 | xargs kill -9
   
   # Kill all Metro processes
   pkill -f "metro"
   ```

3. **Use Different Port**:
   ```bash
   npx expo start --port 8082
   ```

#### Issue: "Unable to resolve module"
**Symptoms**:
- Import errors for installed packages
- Module not found errors
- Inconsistent module resolution

**Solutions**:
1. **Clean Install**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Clear All Caches**:
   ```bash
   npx expo start --clear
   rm -rf node_modules/.cache
   ```

3. **Check Import Paths**:
   ```javascript
   // Ensure correct relative paths
   import authService from '../services/auth'; // ✅ Correct
   import authService from 'services/auth';    // ❌ Incorrect
   ```

### NativeWind/Tailwind Issues

#### Issue: "Tailwind classes not working"
**Symptoms**:
- Styles not applied
- className props ignored
- Default React Native styles showing

**Solutions**:
1. **Verify NativeWind Version**:
   ```bash
   npm list nativewind
   # Should show v2.0.11 or compatible
   ```

2. **Check Babel Configuration**:
   ```javascript
   // babel.config.js should include
   plugins: ['nativewind/babel']
   ```

3. **Restart with Clear Cache**:
   ```bash
   npx expo start --clear
   ```

4. **Test Simple Classes**:
   ```jsx
   // Test with basic classes first
   <View className="bg-red-500 p-4">
     <Text className="text-white">Test</Text>
   </View>
   ```

#### Issue: "Babel configuration error"
**Symptoms**:
- ".plugins is not a valid Plugin property"
- Babel compilation errors
- App won't start

**Solutions**:
1. **Use Correct NativeWind Version**:
   ```bash
   npm uninstall nativewind tailwindcss
   npm install nativewind@^2.0.11 tailwindcss@3.3.0
   ```

2. **Update Babel Config**:
   ```javascript
   // babel.config.js
   module.exports = function(api) {
     const isTest = api.env('test');
     api.cache(true);
     
     return {
       presets: isTest 
         ? [
             ['@babel/preset-env', { targets: { node: 'current' } }],
             ['@babel/preset-react', { runtime: 'automatic' }],
           ]
         : ['babel-preset-expo'],
       plugins: isTest ? [] : ['nativewind/babel'],
     };
   };
   ```

## Testing Issues

### Jest Configuration Problems

#### Issue: "Tests failing with import errors"
**Symptoms**:
- Cannot resolve module in tests
- React Native components not mocking properly
- Babel configuration conflicts

**Solutions**:
1. **Install Missing Dependencies**:
   ```bash
   npm install --save-dev jest-environment-jsdom @babel/preset-env @babel/preset-react react-native-web react-dom
   ```

2. **Check Jest Configuration**:
   ```json
   // package.json
   "jest": {
     "preset": "jest-expo",
     "testEnvironment": "jsdom"
   }
   ```

3. **Verify Babel Test Config**:
   ```javascript
   // babel.config.js should handle test environment
   const isTest = api.env('test');
   ```

#### Issue: "Auth service errors in tests"
**Symptoms**:
- Supabase client initialization errors
- AsyncStorage not available in tests
- Auth state management issues

**Solutions**:
1. **Mock Auth Service**:
   ```javascript
   // __mocks__/authService.js
   export default {
     signIn: jest.fn(),
     signOut: jest.fn(),
     getCurrentUser: jest.fn(),
     isAuthenticated: jest.fn(() => false),
   };
   ```

2. **Mock AsyncStorage**:
   ```javascript
   // jest.setup.js
   import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';
   jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);
   ```

## Platform-Specific Issues

### iOS Issues

#### Issue: "iOS Simulator not opening"
**Symptoms**:
- Simulator doesn't launch when pressing 'i'
- Xcode command line tools errors
- Simulator crashes on launch

**Solutions**:
1. **Install Xcode Command Line Tools**:
   ```bash
   xcode-select --install
   ```

2. **Reset iOS Simulator**:
   ```bash
   xcrun simctl erase all
   ```

3. **Check Simulator Path**:
   ```bash
   xcrun simctl list devices
   ```

### Android Issues

#### Issue: "Android emulator not starting"
**Symptoms**:
- Emulator doesn't launch when pressing 'a'
- ANDROID_HOME not set errors
- Emulator crashes or freezes

**Solutions**:
1. **Set Environment Variables**:
   ```bash
   # Add to ~/.bashrc or ~/.zshrc
   export ANDROID_HOME=$HOME/Library/Android/sdk
   export PATH=$PATH:$ANDROID_HOME/emulator
   export PATH=$PATH:$ANDROID_HOME/platform-tools
   ```

2. **Cold Boot Emulator**:
   ```bash
   emulator -avd <device_name> -cold-boot
   ```

3. **Check Available Devices**:
   ```bash
   emulator -list-avds
   ```

### Web Issues

#### Issue: "Web version not loading"
**Symptoms**:
- Blank page on localhost:8081
- Console errors about missing modules
- Webpack compilation errors

**Solutions**:
1. **Clear Browser Cache**:
   - Hard refresh (Cmd+Shift+R or Ctrl+Shift+R)
   - Clear browser cache and cookies

2. **Check Console Errors**:
   - Open browser developer tools
   - Look for specific error messages
   - Check Network tab for failed requests

3. **Restart Development Server**:
   ```bash
   npx expo start --clear
   ```

## Performance Issues

### Slow Development Server

#### Issue: "Metro bundler very slow"
**Symptoms**:
- Long bundle times
- Slow hot reload
- High CPU usage

**Solutions**:
1. **Exclude Unnecessary Files**:
   ```javascript
   // metro.config.js
   resolver: {
     blacklistRE: /node_modules\/.*\/node_modules\/.*/,
   }
   ```

2. **Disable Source Maps** (development only):
   ```javascript
   // metro.config.js
   transformer: {
     minifierConfig: {
       keep_fnames: true,
       mangle: {
         keep_fnames: true,
       },
     },
   }
   ```

3. **Close Unnecessary Applications**:
   - Close other development servers
   - Quit resource-intensive applications
   - Monitor system resources

## Network Issues

### API Connection Problems

#### Issue: "Network request failed"
**Symptoms**:
- API calls timeout
- Connection refused errors
- Intermittent network failures

**Solutions**:
1. **Check API Server Status**:
   ```bash
   curl -I http://localhost:3000/api/health
   ```

2. **Verify Network Configuration**:
   ```javascript
   // Test with different base URL
   API_BASE_URL=https://api.example.com
   ```

3. **Increase Timeout**:
   ```javascript
   // src/config/api.js
   TIMEOUT: 60000, // Increase to 60 seconds
   ```

4. **Check Firewall Settings**:
   - Ensure development ports are not blocked
   - Check corporate firewall settings
   - Verify VPN configuration

## Getting Help

### Debug Information to Collect

When reporting issues, include:

1. **Environment Information**:
   ```bash
   node --version
   npm --version
   expo --version
   ```

2. **Package Versions**:
   ```bash
   npm list nativewind tailwindcss @supabase/supabase-js
   ```

3. **Error Messages**:
   - Full error stack traces
   - Console output
   - Network request details

4. **Configuration Files**:
   - babel.config.js
   - metro.config.js
   - tailwind.config.js
   - Environment variables (without sensitive data)

### Support Channels

- **Documentation**: Check implementation guides first
- **GitHub Issues**: Search existing issues
- **Community Forums**: Expo, React Native communities
- **Stack Overflow**: Tag with relevant technologies
