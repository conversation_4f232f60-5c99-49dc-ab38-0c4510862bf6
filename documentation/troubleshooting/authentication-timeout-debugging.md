# Authentication Timeout Debugging Guide

## Overview

This guide provides step-by-step instructions for debugging Supabase authentication timeout issues, specifically the "Network request timed out" error in the OnRoad React Native app.

## Error Symptoms

### Common Error Messages
- `[AuthRetryableFetchError: Network request timed out]`
- `Network request failed`
- `Sign in error: Network request timed out`
- Connection timeout errors during login

### When These Errors Occur
- During user login attempts
- When calling `authService.signIn()`
- On slow or unstable network connections
- When Supabase servers are experiencing high load

## Debugging Steps

### Step 1: Enable Debug Logging

1. **Set Debug Mode in Environment**:
   ```bash
   # In .env.development
   DEBUG_MODE=true
   ```

2. **Restart Development Server**:
   ```bash
   npm start --clear
   ```

3. **Check Console Output**:
   Look for detailed authentication logs with `[AUTH-*]` prefixes.

### Step 2: Run Network Diagnostics

1. **Use Built-in Network Test**:
   - Open the LoginScreen
   - Tap "Network Diagnostics" button at the bottom
   - Review the test results

2. **Interpret Test Results**:
   ```
   ✅ internetConnectivity - Basic internet works
   ✅ supabaseReachability - Supabase endpoint accessible
   ✅ supabaseAuth - Auth endpoint responding
   ✅ dnsResolution - DNS lookup working
   ```

3. **Common Failure Patterns**:
   - All tests fail: No internet connection
   - Only Supabase tests fail: Supabase configuration issue
   - Slow response times (>10s): Network performance issue

### Step 3: Verify Supabase Configuration

1. **Check Environment Variables**:
   ```bash
   # Verify these are set correctly in .env.development
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_ANON_KEY=your-actual-anon-key
   SUPABASE_TIMEOUT=30000
   ```

2. **Validate Configuration**:
   - URL should start with `https://`
   - Anon key should be ~100+ characters
   - Timeout should be reasonable (10-60 seconds)

3. **Test Configuration Manually**:
   ```bash
   # Test Supabase endpoint directly
   curl -I https://your-project-id.supabase.co/rest/v1/
   ```

### Step 4: Analyze Network Timing

1. **Check Debug Logs for Timing**:
   ```
   [AUTH-TIMING] Starting signInWithPassword
   [AUTH-TIMING] signInWithPassword completed in 25000ms
   ```

2. **Identify Slow Operations**:
   - Normal: < 5 seconds
   - Slow: 5-15 seconds
   - Timeout: > 30 seconds

3. **Network Performance Indicators**:
   - Consistent slow times: Network bandwidth issue
   - Intermittent timeouts: Network stability issue
   - Always timeout: Configuration or connectivity issue

### Step 5: Test Different Network Conditions

1. **WiFi vs Mobile Data**:
   - Test on different networks
   - Compare performance between WiFi and cellular

2. **Network Speed Test**:
   - Use device's built-in speed test
   - Ensure adequate bandwidth (>1 Mbps recommended)

3. **VPN/Proxy Issues**:
   - Disable VPN if active
   - Check corporate firewall settings
   - Test on different network environments

## Common Solutions

### Solution 1: Increase Timeout Values

1. **Update Environment Variables**:
   ```bash
   # Increase timeout for slower networks
   SUPABASE_TIMEOUT=60000  # 60 seconds
   SUPABASE_RETRY_ATTEMPTS=5
   SUPABASE_RETRY_DELAY=2000
   ```

2. **Restart Development Server**:
   ```bash
   npm start --clear
   ```

### Solution 2: Fix Supabase Configuration

1. **Verify Project Settings**:
   - Log into Supabase dashboard
   - Go to Settings → API
   - Copy correct Project URL and anon key

2. **Update Environment File**:
   ```bash
   SUPABASE_URL=https://correct-project-id.supabase.co
   SUPABASE_ANON_KEY=correct-anon-key-here
   ```

3. **Check Project Status**:
   - Ensure Supabase project is active
   - Verify no service outages
   - Check project billing status

### Solution 3: Network Optimization

1. **Optimize Fetch Configuration**:
   The app automatically includes enhanced fetch configuration with:
   - Custom timeout handling
   - Better error messages
   - Retry logic for failed requests

2. **Test on Stable Network**:
   - Use reliable WiFi connection
   - Avoid congested networks
   - Test during off-peak hours

### Solution 4: Alternative Authentication Methods

1. **Test with Different Credentials**:
   - Try with known working account
   - Test user registration flow
   - Verify email confirmation status

2. **Use Supabase Dashboard**:
   - Test authentication in Supabase dashboard
   - Verify user exists and is confirmed
   - Check authentication logs

## Advanced Debugging

### Enable Detailed Network Logging

1. **Add Network Interceptor** (for development):
   ```javascript
   // In src/config/supabase.js
   global.fetch = (url, options) => {
     console.log('FETCH REQUEST:', url, options);
     return originalFetch(url, options).then(response => {
       console.log('FETCH RESPONSE:', response.status, response.statusText);
       return response;
     });
   };
   ```

### Monitor Network Requests

1. **Use React Native Debugger**:
   - Enable network inspection
   - Monitor all HTTP requests
   - Check request/response timing

2. **Browser Developer Tools** (for web):
   - Open Network tab
   - Monitor Supabase API calls
   - Check for failed requests

### Test Supabase Directly

1. **Use Supabase JavaScript Client**:
   ```javascript
   // Test in browser console or Node.js
   import { createClient } from '@supabase/supabase-js'
   
   const supabase = createClient(
     'your-project-url',
     'your-anon-key'
   )
   
   const { data, error } = await supabase.auth.signInWithPassword({
     email: '<EMAIL>',
     password: 'password'
   })
   ```

## Prevention Strategies

### 1. Robust Error Handling
- Implement retry logic for network failures
- Provide clear error messages to users
- Offer alternative authentication methods

### 2. Network Monitoring
- Monitor authentication success rates
- Track average response times
- Set up alerts for high failure rates

### 3. User Experience
- Show loading indicators during authentication
- Provide network status feedback
- Offer offline capabilities where possible

### 4. Configuration Management
- Use environment-specific configurations
- Implement configuration validation
- Monitor configuration changes

## Getting Help

### Information to Collect

When reporting authentication timeout issues, include:

1. **Error Details**:
   - Complete error message
   - Error stack trace
   - Timing information from logs

2. **Environment Information**:
   - Network type (WiFi/cellular)
   - Device type and OS version
   - App version and build

3. **Configuration**:
   - Supabase project ID (without sensitive keys)
   - Timeout values
   - Network test results

4. **Reproduction Steps**:
   - Exact steps to reproduce
   - Frequency of occurrence
   - Network conditions when it happens

### Support Channels

- **Supabase Support**: For Supabase-specific issues
- **React Native Community**: For React Native networking issues
- **Project Documentation**: Check troubleshooting guides
- **GitHub Issues**: Report bugs with detailed information

## Summary

Authentication timeout issues are typically caused by:
1. **Network connectivity problems**
2. **Incorrect Supabase configuration**
3. **Insufficient timeout values**
4. **Supabase service issues**

The enhanced logging and network testing tools in the app provide comprehensive debugging capabilities to identify and resolve these issues quickly.
