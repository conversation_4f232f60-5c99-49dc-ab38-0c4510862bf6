# OpenAPI Specification Analysis

## Overview

This document provides a comprehensive analysis of the OnRoad API based on the OpenAPI 3.0 specification located at `specifications/API Specs/openapi.yaml`.

## API Specification Summary

### Base Information
- **API Title**: OnRoad Real Estate API
- **Version**: 1.0.0
- **Base URL**: Configurable via environment variables
- **Authentication**: Bearer JWT tokens (Supabase)

### Security Requirements

All API endpoints require Bearer JWT authentication:
```yaml
security:
  - bearerAuth: []
```

**Authentication Header Format**:
```
Authorization: Bearer <supabase_jwt_token>
```

## Endpoint Analysis

### Authentication Endpoints

#### POST /firstSignin
- **Purpose**: Execute first sign-in logic for new users
- **Authentication**: Required (Bearer JWT)
- **Request Body**: None
- **Response**: 
  - `200`: Success response
  - `401`: Unauthorized
  - `500`: Internal server error

**Implementation Note**: This endpoint is called after successful Supabase authentication to initialize user data in the backend system.

### Profile Management

#### POST /profiles
- **Purpose**: Create a new user profile
- **Authentication**: Required (Bearer JWT)
- **Request Body**: Profile object with user information
- **Response**:
  - `201`: Profile created successfully
  - `400`: Bad request (validation errors)
  - `401`: Unauthorized
  - `409`: Conflict (profile already exists)

**Profile Schema**:
```json
{
  "id": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "role": "broker|realtor|client",
  "createdAt": "string (ISO 8601)",
  "updatedAt": "string (ISO 8601)"
}
```

### Deal Management

#### GET /deals
- **Purpose**: Retrieve all deals for authenticated user
- **Authentication**: Required (Bearer JWT)
- **Query Parameters**: None specified
- **Response**:
  - `200`: Array of deal objects
  - `401`: Unauthorized

#### POST /deals
- **Purpose**: Create a new deal
- **Authentication**: Required (Bearer JWT)
- **Request Body**: Deal object
- **Response**:
  - `201`: Deal created successfully
  - `400`: Bad request
  - `401`: Unauthorized

#### GET /deals/{dealId}
- **Purpose**: Get specific deal by ID
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `dealId` (string)
- **Response**:
  - `200`: Deal object
  - `401`: Unauthorized
  - `404`: Deal not found

### Property Management

#### GET /dealOptions/{dealId}/properties
- **Purpose**: Get properties associated with a deal
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `dealId` (string)
- **Response**:
  - `200`: Array of property objects
  - `401`: Unauthorized
  - `404`: Deal not found

#### POST /dealOptions/{dealId}/properties
- **Purpose**: Add a new property to a deal
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `dealId` (string)
- **Request Body**: Property object
- **Response**:
  - `201`: Property created
  - `400`: Bad request
  - `401`: Unauthorized

#### PUT /dealOptions/properties/{propertyId}
- **Purpose**: Update an existing property
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `propertyId` (string)
- **Request Body**: Updated property object
- **Response**:
  - `200`: Property updated
  - `400`: Bad request
  - `401`: Unauthorized
  - `404`: Property not found

#### DELETE /dealOptions/properties/{propertyId}
- **Purpose**: Delete a property
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `propertyId` (string)
- **Response**:
  - `204`: Property deleted
  - `401`: Unauthorized
  - `404`: Property not found

### Form Management

#### GET /forms/property/{propertyId}
- **Purpose**: Get forms associated with a property
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `propertyId` (string)
- **Response**:
  - `200`: Array of form objects
  - `401`: Unauthorized

#### POST /forms/property/{propertyId}
- **Purpose**: Create a new form for a property
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `propertyId` (string)
- **Request Body**: Form object
- **Response**:
  - `201`: Form created
  - `400`: Bad request
  - `401`: Unauthorized

#### PUT /forms/{formId}
- **Purpose**: Update an existing form
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `formId` (string)
- **Request Body**: Updated form object
- **Response**:
  - `200`: Form updated
  - `400`: Bad request
  - `401`: Unauthorized
  - `404`: Form not found

#### DELETE /forms/{formId}
- **Purpose**: Delete a form
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `formId` (string)
- **Response**:
  - `204`: Form deleted
  - `401`: Unauthorized
  - `404`: Form not found

### Client Intake Management

#### GET /client/intake/schema/{searchId}
- **Purpose**: Get client intake schema
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `searchId` (string)
- **Response**:
  - `200`: Intake schema object
  - `401`: Unauthorized
  - `404`: Schema not found

#### GET /client/intake/answers/get/{searchId}
- **Purpose**: Get client intake form responses
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `searchId` (string)
- **Response**:
  - `200`: Intake answers object
  - `401`: Unauthorized
  - `404`: Answers not found

#### GET /realtor/client-intake/get-by-dealid/{dealId}
- **Purpose**: Get client intake by deal ID
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `dealId` (string)
- **Response**:
  - `200`: Client intake object
  - `401`: Unauthorized
  - `404`: Intake not found

#### PATCH /realtor/client-intake/push-schema-to-tether/{dealId}
- **Purpose**: Publish client intake template
- **Authentication**: Required (Bearer JWT)
- **Path Parameters**: `dealId` (string)
- **Response**:
  - `200`: Template published successfully
  - `401`: Unauthorized
  - `404`: Deal not found

## Data Models

### Common Response Patterns

#### Success Response
```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully"
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  }
}
```

### Authentication Flow

1. **User Authentication**: Handled by Supabase
2. **JWT Token**: Obtained from Supabase after successful authentication
3. **API Requests**: Include JWT token in Authorization header
4. **First Sign-in**: Call `/firstSignin` endpoint after initial authentication
5. **Subsequent Requests**: Use JWT token for all API calls

## Implementation Considerations

### Error Handling
- **401 Unauthorized**: Token expired or invalid - trigger token refresh
- **403 Forbidden**: Insufficient permissions - show access denied message
- **404 Not Found**: Resource doesn't exist - handle gracefully
- **500 Internal Server Error**: Server error - retry with exponential backoff

### Token Management
- **Automatic Refresh**: Implement automatic token refresh on 401 errors
- **Token Storage**: Secure storage using AsyncStorage
- **Token Validation**: Check token expiration before API calls

### Rate Limiting
- **Retry Logic**: Implement exponential backoff for rate-limited requests
- **Request Queuing**: Queue requests during rate limit periods
- **User Feedback**: Inform users of temporary service limitations

### Offline Support
- **Request Caching**: Cache successful responses for offline access
- **Request Queuing**: Queue failed requests for retry when online
- **Sync Strategy**: Implement data synchronization when connection restored

## Security Considerations

### JWT Token Security
- **Secure Storage**: Store tokens securely using AsyncStorage
- **Token Rotation**: Implement automatic token refresh
- **Logout Cleanup**: Clear all tokens on logout

### API Security
- **HTTPS Only**: Ensure all API calls use HTTPS
- **Request Validation**: Validate all request parameters
- **Error Sanitization**: Don't expose sensitive information in error messages

### Data Privacy
- **Minimal Data**: Only request necessary data
- **Data Encryption**: Encrypt sensitive data in transit and at rest
- **User Consent**: Obtain proper consent for data collection

## Testing Strategy

### Unit Tests
- **Service Layer**: Test all API service methods
- **Error Handling**: Test error scenarios and edge cases
- **Token Management**: Test authentication flows

### Integration Tests
- **API Endpoints**: Test actual API endpoint responses
- **Authentication**: Test complete authentication flow
- **Data Flow**: Test end-to-end data operations

### Mock Testing
- **Development**: Use mock responses during development
- **Testing**: Mock API responses for consistent testing
- **Offline Testing**: Test offline scenarios with mocked data
