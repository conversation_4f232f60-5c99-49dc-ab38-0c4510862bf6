# iOS Simulator Crash Report

## System Information
- Node.js: v24.1.0
- npm: 11.3.0
- Expo CLI: 0.24.13
- macOS: 15.5
- Xcode: Xcode 16.4

## Project Configuration
- React Native: 0.74.5
- Expo SDK: ~53.0.0
- React: 18.2.0

## Available iOS Simulators
    iPhone 15 Pro (80ECE747-76BF-4DC5-AC26-061008C4A120) (Shutdown) 
    iPhone 15 Pro Max (777D5980-2304-41F3-A04C-F10A7AA025A1) (Shutdown) 
    iPhone 15 (9CB8B49C-075F-4570-B92A-5E8A7D30323D) (Shutdown) 
    iPhone 15 Plus (24102EB8-0687-4B0F-BCD4-6B980807E9C2) (Shutdown) 
    iPhone SE (3rd generation) (8DF39982-C646-43F6-B96C-35201C811F16) (Shutdown) 

## Metro Bundler Log (Last 20 lines)
./ios-simulator-diagnostics.sh: line 90: timeout: command not found

## Dependency Issues
No dependency issues found

## Recommended Next Steps
1. Run complete reset: ./complete-reset-procedure.sh
2. Try alternative React Native version
3. Test with minimal app configuration
4. Check iOS Simulator logs in Console.app
