# Expo Development Setup Guide

This guide will help you set up and run the onRoad React Native application using Expo's managed workflow and Expo Go for testing on physical devices.

## 🎯 Migration Complete!

Your project has been successfully migrated from React Native CLI (bare workflow) to **Expo's managed workflow**. This provides:

- ✅ **Easy device testing** with Expo Go app
- ✅ **QR code deployment** - no cables needed
- ✅ **Hot reloading** and live updates
- ✅ **Cross-platform development** (iOS, Android)
- ✅ **Built-in debugging tools**
- ✅ **Over-the-air updates** capability

## 📱 Quick Start

### Step 1: Install Expo Go on Your Device

**iOS:**
1. Open the App Store
2. Search for "Expo Go"
3. Install the app by Expo

**Android:**
1. Open Google Play Store
2. Search for "Expo Go"
3. Install the app by Expo

### Step 2: Start the Development Server

```bash
# Start Expo development server
npm start

# Alternative commands:
npm run start        # Same as above
npm run android      # Start and open Android
npm run ios          # Start and open iOS
```

### Step 3: Connect Your Device

1. **Make sure your device and computer are on the same WiFi network**
2. **Run the start command** - this will show a QR code in your terminal
3. **Scan the QR code**:
   - **iOS**: Open Camera app and point at QR code, tap the notification
   - **Android**: Open Expo Go app and tap "Scan QR Code"

## 🛠 Development Commands

```bash
# Start development server with QR code
npm start

# Start with specific platform
npm run android      # Opens Android emulator/device
npm run ios          # Opens iOS simulator/device

# Development options
npx expo start --clear          # Clear cache and start
npx expo start --offline        # Start in offline mode
npx expo start --tunnel         # Use tunnel for remote access
npx expo start --localhost      # Use localhost only
```

## 🔧 Project Structure

```
onRoad-App/
├── App.js                 # Main app component (Expo entry point)
├── app.json              # Expo configuration
├── babel.config.js       # Babel configuration for Expo
├── package.json          # Dependencies and scripts
├── assets/               # App assets (icons, images)
├── src/
│   ├── components/       # Reusable components
│   ├── screens/          # App screens
│   ├── navigation/       # Navigation setup
│   ├── store/           # State management
│   ├── api/             # API calls
│   ├── hooks/           # Custom hooks
│   ├── theme/           # Styling and themes
│   └── utils/           # Utility functions
└── node_modules/        # Dependencies
```

## 📋 What Changed in Migration

### ✅ Added
- **Expo SDK** (~50.0.0) - Core Expo functionality
- **expo-status-bar** - Expo status bar component
- **@expo/cli** - Expo command line tools
- **babel-preset-expo** - Expo-optimized Babel preset
- **app.json** - Expo configuration file

### ❌ Removed
- React Native CLI dependencies
- Metro configuration (now handled by Expo)
- Webpack configuration
- Native iOS/Android directories
- Bare React Native setup scripts

### 🔄 Modified
- **App.js** - Updated to use Expo StatusBar
- **package.json** - Updated scripts and dependencies
- **babel.config.js** - Simplified for Expo

## 🐛 Debugging Features

### Built-in Debugging
- **Hot Reloading** - Automatic code updates
- **Live Reloading** - Full app refresh on changes
- **Error Overlay** - In-app error display
- **Console Logs** - View logs in Expo Go

### Advanced Debugging
```bash
# Open developer menu on device:
# - iOS: Shake device or press Cmd+D in simulator
# - Android: Shake device or press Cmd+M in emulator

# Available options:
# - Reload
# - Debug Remote JS
# - Enable Hot Reloading
# - Enable Live Reload
# - Start/Stop Performance Monitor
```

### Remote Debugging
1. Open developer menu on device
2. Tap "Debug Remote JS"
3. Opens Chrome DevTools for debugging

## 🌐 Network Configuration

### Same WiFi Network (Recommended)
- Ensure device and computer are on same network
- Use `npm start` (default LAN mode)

### Different Networks / Remote Access
```bash
# Use tunnel mode for remote access
npx expo start --tunnel

# This creates a public URL accessible from anywhere
# Useful for:
# - Different networks
# - Sharing with team members
# - Testing from remote locations
```

## 📱 Device Testing Workflow

1. **Start development server**: `npm start`
2. **Scan QR code** with Expo Go
3. **App loads** on your device
4. **Make code changes** - they appear instantly
5. **Test features** directly on physical device
6. **Debug issues** using developer menu

## 🔄 Hot Reloading

Hot reloading is enabled by default:
- **JavaScript changes** - Update instantly
- **Component changes** - Preserve state
- **Style changes** - Apply immediately
- **New files** - Require app reload

## 🚀 Building for Production

When ready for production builds:

```bash
# Install EAS CLI for building
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android

# Build for both platforms
eas build --platform all
```

## 🛠 Troubleshooting

### QR Code Not Working
1. Ensure same WiFi network
2. Try tunnel mode: `npx expo start --tunnel`
3. Check firewall settings
4. Restart Expo Go app

### App Won't Load
1. Clear Expo cache: `npx expo start --clear`
2. Restart development server
3. Check for JavaScript errors
4. Verify app.json configuration

### Performance Issues
1. Enable production mode: `npx expo start --no-dev`
2. Check for memory leaks
3. Optimize images and assets
4. Use React DevTools for profiling

### Network Issues
```bash
# Use localhost mode if LAN doesn't work
npx expo start --localhost

# Use tunnel for network issues
npx expo start --tunnel
```

## 📚 Additional Resources

- [Expo Documentation](https://docs.expo.dev/)
- [Expo Go App](https://expo.dev/client)
- [React Native Documentation](https://reactnative.dev/)
- [Expo Snack (Online Playground)](https://snack.expo.dev/)

## 🎉 You're Ready!

Your onRoad app is now configured for Expo development. Simply run `npm start`, scan the QR code with Expo Go, and start developing with instant updates on your physical device!

For any issues, check the troubleshooting section or refer to the Expo documentation.
